{"train": [{"input": [[5, 5, 5, 5, 5, 7, 7, 7, 7, 7, 7, 9, 9, 9], [5, 5, 5, 5, 5, 7, 7, 7, 7, 7, 7, 9, 9, 9], [5, 5, 5, 5, 5, 7, 7, 7, 7, 7, 7, 9, 9, 9], [5, 5, 5, 5, 5, 7, 7, 7, 7, 7, 7, 9, 9, 9], [5, 5, 5, 5, 5, 7, 7, 7, 7, 7, 7, 9, 9, 9], [5, 5, 9, 5, 5, 7, 7, 9, 7, 7, 7, 9, 9, 9], [5, 5, 7, 5, 5, 7, 7, 7, 7, 7, 7, 9, 7, 9], [5, 5, 5, 5, 5, 7, 7, 7, 7, 5, 7, 9, 5, 9], [5, 5, 5, 5, 5, 7, 7, 7, 7, 7, 7, 9, 9, 9]], "output": [[9, 9, 9, 7, 7, 7, 7, 7, 7, 5, 5, 5, 5, 5], [9, 9, 9, 7, 7, 7, 7, 7, 7, 5, 5, 5, 5, 5], [9, 9, 9, 7, 7, 7, 7, 7, 7, 5, 5, 5, 5, 5], [9, 9, 9, 7, 7, 7, 7, 7, 7, 5, 5, 5, 5, 5], [9, 9, 9, 7, 7, 7, 7, 7, 7, 5, 5, 5, 5, 5], [9, 9, 9, 7, 7, 9, 7, 7, 7, 5, 5, 9, 5, 5], [9, 7, 9, 7, 7, 7, 7, 7, 7, 5, 5, 7, 5, 5], [9, 5, 9, 7, 7, 7, 7, 5, 7, 5, 5, 5, 5, 5], [9, 9, 9, 7, 7, 7, 7, 7, 7, 5, 5, 5, 5, 5]]}, {"input": [[8, 9, 8, 9, 9, 9, 1, 9, 1, 3, 9, 3], [8, 8, 8, 9, 9, 9, 1, 1, 1, 3, 3, 3], [8, 8, 8, 9, 8, 9, 1, 8, 1, 3, 8, 3], [8, 8, 8, 9, 9, 9, 1, 1, 1, 3, 3, 3], [8, 3, 8, 9, 3, 9, 1, 3, 1, 3, 3, 3], [8, 8, 8, 9, 9, 9, 1, 1, 1, 3, 3, 3], [8, 1, 8, 9, 1, 9, 1, 1, 1, 3, 1, 3]], "output": [[9, 9, 9, 1, 9, 1, 3, 9, 3, 8, 9, 8], [9, 9, 9, 1, 1, 1, 3, 3, 3, 8, 8, 8], [9, 8, 9, 1, 8, 1, 3, 8, 3, 8, 8, 8], [9, 9, 9, 1, 1, 1, 3, 3, 3, 8, 8, 8], [9, 3, 9, 1, 3, 1, 3, 3, 3, 8, 3, 8], [9, 9, 9, 1, 1, 1, 3, 3, 3, 8, 8, 8], [9, 1, 9, 1, 1, 1, 3, 1, 3, 8, 1, 8]]}, {"input": [[8, 8, 8, 8, 8, 2, 2, 2, 2, 1, 1, 1, 1], [8, 8, 8, 1, 8, 2, 2, 2, 2, 1, 1, 1, 1], [8, 8, 8, 2, 8, 2, 2, 2, 2, 1, 1, 1, 1], [8, 8, 8, 8, 8, 2, 2, 2, 2, 1, 1, 1, 1], [8, 8, 8, 8, 8, 2, 2, 8, 2, 1, 8, 1, 1], [8, 8, 8, 8, 8, 2, 1, 2, 2, 1, 1, 2, 1], [8, 8, 8, 8, 8, 2, 2, 2, 2, 1, 1, 1, 1], [8, 8, 8, 8, 8, 2, 2, 2, 2, 1, 1, 1, 1]], "output": [[2, 2, 2, 2, 1, 1, 1, 1, 8, 8, 8, 8, 8], [2, 2, 2, 2, 1, 1, 1, 1, 8, 8, 8, 1, 8], [2, 2, 2, 2, 1, 1, 1, 1, 8, 8, 8, 2, 8], [2, 2, 2, 2, 1, 1, 1, 1, 8, 8, 8, 8, 8], [2, 2, 8, 2, 1, 8, 1, 1, 8, 8, 8, 8, 8], [2, 1, 2, 2, 1, 1, 2, 1, 8, 8, 8, 8, 8], [2, 2, 2, 2, 1, 1, 1, 1, 8, 8, 8, 8, 8], [2, 2, 2, 2, 1, 1, 1, 1, 8, 8, 8, 8, 8]]}, {"input": [[5, 5, 5, 7, 7, 7, 1, 1, 1, 1], [5, 1, 5, 7, 7, 7, 1, 1, 1, 1], [5, 5, 5, 7, 7, 7, 1, 1, 1, 1], [5, 5, 5, 7, 5, 7, 1, 7, 1, 1], [5, 7, 5, 7, 1, 7, 1, 1, 1, 1], [5, 5, 5, 7, 7, 7, 1, 1, 1, 1], [5, 5, 5, 7, 7, 7, 1, 1, 5, 1], [5, 5, 5, 7, 7, 7, 1, 1, 1, 1]], "output": [[7, 7, 7, 1, 1, 1, 1, 5, 5, 5], [7, 7, 7, 1, 1, 1, 1, 5, 1, 5], [7, 7, 7, 1, 1, 1, 1, 5, 5, 5], [7, 5, 7, 1, 7, 1, 1, 5, 5, 5], [7, 1, 7, 1, 1, 1, 1, 5, 7, 5], [7, 7, 7, 1, 1, 1, 1, 5, 5, 5], [7, 7, 7, 1, 1, 5, 1, 5, 5, 5], [7, 7, 7, 1, 1, 1, 1, 5, 5, 5]]}], "test": [{"input": [[9, 9, 9, 4, 4, 4, 1, 1, 1, 1, 1, 1, 1], [9, 9, 9, 4, 4, 4, 1, 1, 1, 1, 1, 1, 1], [9, 9, 9, 4, 9, 4, 1, 1, 1, 9, 1, 1, 1], [9, 1, 9, 4, 1, 4, 1, 1, 1, 1, 1, 1, 1], [9, 4, 9, 4, 4, 4, 1, 1, 1, 4, 1, 1, 1], [9, 9, 9, 4, 4, 4, 1, 1, 1, 1, 1, 1, 1], [9, 9, 9, 4, 4, 4, 1, 1, 1, 1, 1, 1, 1], [9, 9, 9, 4, 4, 4, 1, 1, 1, 1, 1, 1, 1]], "output": [[4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 9, 9, 9], [4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 9, 9, 9], [4, 9, 4, 1, 1, 1, 9, 1, 1, 1, 9, 9, 9], [4, 1, 4, 1, 1, 1, 1, 1, 1, 1, 9, 1, 9], [4, 4, 4, 1, 1, 1, 4, 1, 1, 1, 9, 4, 9], [4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 9, 9, 9], [4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 9, 9, 9], [4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 9, 9, 9]]}, {"input": [[1, 1, 1, 1, 7, 7, 7, 8, 8, 8, 8, 8, 3, 3, 3], [1, 1, 1, 1, 7, 7, 7, 8, 8, 8, 8, 8, 3, 3, 3], [1, 3, 1, 1, 7, 3, 7, 8, 8, 8, 3, 8, 3, 3, 3], [1, 7, 1, 1, 7, 7, 7, 8, 8, 8, 7, 8, 3, 7, 3], [1, 8, 1, 1, 7, 8, 7, 8, 8, 8, 8, 8, 3, 8, 3], [1, 1, 1, 1, 7, 1, 7, 8, 8, 8, 1, 8, 3, 1, 3], [1, 1, 1, 1, 7, 7, 7, 8, 8, 8, 8, 8, 3, 3, 3]], "output": [[7, 7, 7, 8, 8, 8, 8, 8, 3, 3, 3, 1, 1, 1, 1], [7, 7, 7, 8, 8, 8, 8, 8, 3, 3, 3, 1, 1, 1, 1], [7, 3, 7, 8, 8, 8, 3, 8, 3, 3, 3, 1, 3, 1, 1], [7, 7, 7, 8, 8, 8, 7, 8, 3, 7, 3, 1, 7, 1, 1], [7, 8, 7, 8, 8, 8, 8, 8, 3, 8, 3, 1, 8, 1, 1], [7, 1, 7, 8, 8, 8, 1, 8, 3, 1, 3, 1, 1, 1, 1], [7, 7, 7, 8, 8, 8, 8, 8, 3, 3, 3, 1, 1, 1, 1]]}]}