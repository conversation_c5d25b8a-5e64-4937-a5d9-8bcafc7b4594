{"train": [{"input": [[3, 4, 0, 5, 0, 0, 3, 0, 5, 8, 0, 7, 0, 0, 0], [0, 0, 4, 5, 8, 8, 0, 0, 0, 0, 7, 3, 3, 0, 0], [0, 8, 3, 5, 0, 0, 5, 0, 0, 1, 0, 2, 0, 0, 9], [5, 5, 5, 5, 6, 1, 0, 9, 0, 0, 3, 3, 0, 6, 0], [3, 7, 0, 0, 0, 5, 0, 0, 0, 0, 4, 0, 0, 0, 0], [0, 4, 0, 0, 5, 5, 6, 0, 0, 0, 0, 0, 1, 5, 0], [0, 2, 1, 0, 0, 0, 0, 0, 4, 9, 0, 9, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 7, 2, 2, 0, 0, 9, 8], [1, 0, 0, 0, 1, 0, 3, 7, 0, 0, 0, 7, 0, 0, 3], [0, 0, 1, 2, 0, 9, 3, 4, 0, 0, 1, 0, 0, 2, 9], [0, 9, 0, 0, 8, 0, 0, 0, 4, 0, 0, 6, 0, 8, 4], [7, 7, 6, 0, 0, 0, 0, 8, 3, 0, 0, 0, 8, 2, 7], [0, 9, 0, 0, 2, 0, 4, 0, 0, 0, 0, 0, 0, 1, 6], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 4, 0, 9, 8, 0], [4, 0, 0, 0, 9, 0, 1, 1, 7, 9, 0, 0, 0, 8, 0]], "output": [[3, 4, 0, 5, 0, 0, 3, 0, 5, 8, 0, 7, 0, 0, 0], [0, 0, 4, 5, 8, 8, 0, 0, 0, 0, 7, 3, 3, 0, 0], [0, 8, 3, 5, 0, 0, 5, 0, 0, 1, 0, 2, 0, 0, 9], [5, 5, 5, 5, 6, 1, 0, 9, 0, 0, 3, 3, 0, 6, 0], [3, 7, 0, 0, 0, 5, 0, 0, 0, 0, 4, 0, 0, 0, 0], [0, 4, 0, 0, 5, 5, 6, 0, 0, 0, 0, 0, 1, 5, 0], [0, 2, 1, 0, 0, 0, 0, 0, 4, 9, 0, 9, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 7, 2, 2, 0, 0, 9, 8], [1, 0, 0, 0, 1, 5, 5, 5, 5, 5, 0, 7, 0, 0, 3], [0, 0, 1, 2, 0, 5, 3, 4, 0, 5, 1, 0, 0, 2, 9], [0, 9, 0, 0, 8, 5, 0, 0, 4, 5, 0, 6, 0, 8, 4], [7, 7, 6, 0, 0, 5, 0, 8, 3, 5, 0, 0, 8, 2, 7], [0, 9, 0, 0, 2, 5, 5, 5, 5, 5, 0, 0, 0, 1, 6], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 4, 0, 9, 8, 0], [4, 0, 0, 0, 9, 0, 1, 1, 7, 9, 0, 0, 0, 8, 0]]}, {"input": [[0, 7, 6, 5, 0, 0, 0, 0, 1, 4, 5, 6, 0, 0, 8], [7, 0, 0, 5, 0, 0, 0, 0, 3, 0, 0, 0, 3, 0, 6], [0, 9, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 2], [5, 5, 5, 5, 4, 0, 0, 0, 4, 0, 9, 0, 9, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0], [2, 3, 6, 0, 0, 0, 7, 6, 0, 0, 9, 4, 0, 0, 4], [0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 9, 0, 0, 0, 0, 9, 0, 8, 7, 0, 0, 0, 0, 0], [0, 6, 1, 0, 7, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0], [1, 0, 5, 4, 0, 0, 8, 0, 0, 0, 0, 2, 2, 0, 6], [3, 0, 6, 0, 2, 0, 0, 0, 0, 4, 0, 0, 0, 6, 0], [4, 1, 0, 0, 0, 0, 1, 0, 7, 0, 0, 0, 0, 4, 0], [0, 2, 0, 0, 7, 0, 0, 9, 7, 6, 0, 0, 5, 3, 0], [4, 0, 4, 1, 0, 0, 8, 1, 8, 0, 0, 9, 4, 7, 7], [0, 8, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 5, 1, 6]], "output": [[0, 7, 6, 5, 0, 0, 0, 0, 1, 4, 5, 6, 0, 0, 8], [7, 0, 0, 5, 0, 0, 0, 0, 3, 0, 0, 0, 3, 0, 6], [0, 9, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 2], [5, 5, 5, 5, 4, 0, 0, 0, 4, 0, 9, 0, 9, 0, 0], [0, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0, 2, 0, 0], [2, 3, 6, 0, 5, 0, 7, 6, 5, 0, 9, 4, 0, 0, 4], [0, 0, 0, 0, 5, 7, 0, 0, 5, 0, 0, 3, 0, 0, 0], [0, 9, 0, 0, 5, 0, 9, 0, 5, 7, 0, 0, 0, 0, 0], [0, 6, 1, 0, 5, 5, 5, 5, 5, 0, 0, 0, 7, 0, 0], [1, 0, 5, 4, 0, 0, 8, 0, 0, 0, 0, 2, 2, 0, 6], [3, 0, 6, 0, 2, 0, 0, 0, 0, 4, 0, 0, 0, 6, 0], [4, 1, 0, 0, 0, 0, 1, 0, 7, 0, 0, 0, 0, 4, 0], [0, 2, 0, 0, 7, 0, 0, 9, 7, 6, 0, 0, 5, 3, 0], [4, 0, 4, 1, 0, 0, 8, 1, 8, 0, 0, 9, 4, 7, 7], [0, 8, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 5, 1, 6]]}, {"input": [[4, 0, 2, 5, 0, 0, 0, 2, 6, 9, 0, 0, 5, 0, 0], [0, 7, 0, 5, 0, 8, 5, 8, 0, 7, 0, 0, 0, 8, 8], [0, 6, 6, 5, 7, 0, 3, 5, 0, 0, 0, 4, 7, 0, 0], [5, 5, 5, 5, 8, 0, 1, 9, 0, 0, 0, 0, 5, 0, 0], [8, 0, 0, 0, 0, 0, 1, 0, 3, 9, 8, 0, 0, 0, 0], [0, 2, 0, 0, 0, 6, 6, 4, 0, 9, 0, 0, 1, 7, 0], [8, 0, 6, 0, 0, 0, 8, 3, 0, 0, 0, 0, 0, 0, 9], [3, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 0, 2, 0, 0, 4, 0, 2, 0, 3, 2, 0, 0], [0, 0, 1, 0, 0, 0, 0, 0, 7, 0, 0, 0, 5, 0, 8], [0, 9, 4, 4, 0, 0, 4, 0, 6, 6, 0, 7, 0, 0, 0], [7, 0, 0, 0, 9, 0, 0, 8, 0, 0, 0, 5, 0, 0, 0], [0, 6, 0, 0, 1, 0, 0, 7, 7, 0, 0, 0, 4, 0, 0], [0, 0, 0, 4, 0, 5, 0, 0, 0, 0, 7, 0, 5, 0, 0], [8, 0, 9, 8, 5, 0, 0, 0, 0, 0, 3, 0, 4, 0, 0]], "output": [[4, 0, 2, 5, 0, 0, 0, 2, 6, 9, 0, 0, 5, 0, 0], [0, 7, 0, 5, 0, 8, 5, 8, 0, 7, 0, 0, 0, 8, 8], [0, 6, 6, 5, 7, 0, 3, 5, 0, 0, 0, 4, 7, 0, 0], [5, 5, 5, 5, 8, 0, 1, 9, 0, 0, 0, 0, 5, 0, 0], [8, 0, 0, 0, 0, 0, 1, 0, 3, 9, 8, 0, 0, 0, 0], [0, 2, 0, 0, 0, 6, 6, 4, 0, 9, 0, 0, 1, 7, 0], [8, 0, 6, 0, 0, 0, 8, 3, 0, 0, 0, 0, 0, 0, 9], [3, 0, 0, 2, 0, 0, 5, 5, 5, 5, 5, 8, 0, 0, 0], [0, 0, 0, 0, 2, 0, 5, 4, 0, 2, 5, 3, 2, 0, 0], [0, 0, 1, 0, 0, 0, 5, 0, 7, 0, 5, 0, 5, 0, 8], [0, 9, 4, 4, 0, 0, 5, 0, 6, 6, 5, 7, 0, 0, 0], [7, 0, 0, 0, 9, 0, 5, 5, 5, 5, 5, 5, 0, 0, 0], [0, 6, 0, 0, 1, 0, 0, 7, 7, 0, 0, 0, 4, 0, 0], [0, 0, 0, 4, 0, 5, 0, 0, 0, 0, 7, 0, 5, 0, 0], [8, 0, 9, 8, 5, 0, 0, 0, 0, 0, 3, 0, 4, 0, 0]]}], "test": [{"input": [[0, 7, 3, 5, 0, 0, 0, 0, 0, 0, 0, 3, 5, 4, 0], [1, 0, 3, 5, 2, 0, 1, 0, 0, 0, 0, 8, 0, 0, 0], [1, 0, 0, 5, 6, 0, 0, 9, 9, 0, 5, 0, 0, 0, 9], [5, 5, 5, 5, 0, 0, 2, 1, 0, 0, 3, 0, 0, 0, 0], [3, 0, 0, 3, 1, 8, 5, 0, 5, 2, 0, 0, 5, 0, 0], [4, 0, 9, 2, 0, 0, 1, 0, 2, 0, 0, 0, 0, 0, 0], [0, 0, 2, 0, 0, 0, 9, 5, 4, 0, 8, 0, 0, 5, 5], [0, 7, 0, 0, 0, 5, 5, 7, 0, 0, 1, 0, 0, 0, 1], [0, 0, 0, 3, 0, 7, 3, 7, 0, 0, 0, 0, 7, 0, 0], [0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 0, 3, 0, 0, 0, 3, 4, 0, 7, 3, 0, 2], [0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 1, 0, 3, 0, 0], [0, 0, 5, 2, 2, 2, 0, 0, 0, 0, 1, 0, 0, 2, 0], [0, 0, 3, 0, 0, 5, 4, 7, 0, 0, 0, 0, 0, 3, 5], [8, 0, 0, 1, 7, 1, 0, 8, 0, 8, 2, 0, 0, 0, 4]], "output": [[0, 7, 3, 5, 0, 0, 0, 0, 0, 0, 0, 3, 5, 4, 0], [1, 0, 3, 5, 2, 0, 1, 0, 0, 0, 0, 8, 0, 0, 0], [1, 0, 0, 5, 6, 0, 0, 9, 9, 0, 5, 0, 0, 0, 9], [5, 5, 5, 5, 0, 0, 2, 1, 0, 0, 3, 0, 0, 0, 0], [3, 0, 0, 3, 1, 8, 5, 0, 5, 2, 0, 0, 5, 0, 0], [4, 0, 9, 2, 0, 0, 1, 0, 2, 0, 0, 0, 0, 0, 0], [0, 0, 2, 0, 0, 0, 9, 5, 4, 0, 8, 0, 0, 5, 5], [0, 7, 0, 0, 0, 5, 5, 7, 0, 0, 1, 0, 0, 0, 1], [0, 0, 0, 3, 0, 7, 3, 7, 0, 0, 0, 0, 7, 0, 0], [0, 0, 0, 0, 0, 0, 0, 9, 0, 5, 5, 5, 5, 5, 0], [0, 0, 0, 0, 3, 0, 0, 0, 3, 5, 0, 7, 3, 5, 2], [0, 2, 2, 0, 0, 0, 0, 0, 0, 5, 1, 0, 3, 5, 0], [0, 0, 5, 2, 2, 2, 0, 0, 0, 5, 1, 0, 0, 5, 0], [0, 0, 3, 0, 0, 5, 4, 7, 0, 5, 5, 5, 5, 5, 5], [8, 0, 0, 1, 7, 1, 0, 8, 0, 8, 2, 0, 0, 0, 4]]}]}