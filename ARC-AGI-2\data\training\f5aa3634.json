{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 2, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 2, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 2, 0, 0, 0, 0, 0], [0, 0, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 0, 6, 8, 0, 0], [0, 0, 8, 8, 8, 0, 0, 0, 6, 6, 8, 0, 0], [0, 0, 5, 5, 5, 0, 0, 0, 0, 4, 4, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0]], "output": [[0, 8, 0], [8, 8, 8], [5, 5, 5]]}, {"input": [[0, 0, 5, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 5, 3, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 3, 0, 0, 0, 0, 0, 0, 7, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 0], [0, 0, 3, 5, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 0, 0, 0, 0, 0, 5, 8, 8, 0], [0, 0, 3, 2, 0, 0, 0, 0, 3, 5, 3, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 5, 8, 8], [3, 5, 3, 8], [0, 3, 3, 0]]}, {"input": [[0, 0, 2, 8, 0, 0, 0, 0, 0, 0, 0, 5, 9, 0, 0], [0, 0, 8, 2, 0, 0, 0, 0, 0, 7, 7, 5, 9, 0, 0], [0, 8, 8, 0, 0, 0, 0, 0, 0, 0, 5, 7, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 6, 6, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 3, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 4, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 9, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 5, 9, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 7, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 5, 9], [7, 7, 5, 9], [0, 5, 7, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 0], [0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 8, 5, 5, 8, 0], [0, 0, 0, 5, 9, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 8, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 4, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 2, 2, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 7, 2, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8, 3, 0], [0, 0, 7, 7, 0, 0, 0, 0, 0, 0, 0, 3, 4, 3, 0], [0, 0, 0, 7, 0, 0, 0, 0, 2, 0, 0, 0, 4, 0, 0], [0, 0, 0, 0, 0, 0, 0, 1, 2, 0, 0, 0, 4, 0, 0], [0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[3, 8, 3], [3, 4, 3], [0, 4, 0], [0, 4, 0]]}]}