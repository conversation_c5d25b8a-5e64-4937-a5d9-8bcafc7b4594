#!/usr/bin/env python3
"""
Simple test for advanced retrieval system components.
"""

import sys
import numpy as np
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

def test_basic_imports():
    """Test basic imports."""
    print("Testing basic imports...")
    
    try:
        from advanced_retrieval import AdvancedFeatureVectorizer, AdvancedSimilarityMetrics
        print("✓ Advanced retrieval imports successful")
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_feature_vectorizer():
    """Test feature vectorizer with simple data."""
    print("\nTesting feature vectorizer...")
    
    try:
        from advanced_retrieval import AdvancedFeatureVectorizer
        
        # Create simple test features
        features1 = {
            'color_num_colors': 3,
            'spatial_width': 5,
            'spatial_height': 5,
            'symmetry_horizontal_symmetry': True,
            'shape_num_rectangles': 1
        }
        
        features2 = {
            'color_num_colors': 2,
            'spatial_width': 3,
            'spatial_height': 3,
            'symmetry_horizontal_symmetry': False,
            'shape_num_rectangles': 0
        }
        
        vectorizer = AdvancedFeatureVectorizer()
        
        # Test fit_transform
        vectors = vectorizer.fit_transform([features1, features2])
        print(f"✓ Vectorized features: shape {vectors.shape}")
        
        # Test single transform
        single_vector = vectorizer.transform(features1)
        print(f"✓ Single transform: shape {single_vector.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Feature vectorizer failed: {e}")
        return False

def test_similarity_metrics():
    """Test similarity metrics."""
    print("\nTesting similarity metrics...")
    
    try:
        from advanced_retrieval import AdvancedSimilarityMetrics
        
        # Simple test features
        features1 = {
            'grid_shape': (3, 3),
            'color_unique_colors': [0, 1, 2],
            'color_num_colors': 3,
            'symmetry_horizontal_symmetry': True
        }
        
        features2 = {
            'grid_shape': (3, 3),
            'color_unique_colors': [0, 1, 2],
            'color_num_colors': 3,
            'symmetry_horizontal_symmetry': True
        }
        
        similarity = AdvancedSimilarityMetrics.arc_specific_similarity(features1, features2)
        print(f"✓ ARC similarity (identical): {similarity:.3f}")
        
        # Test with different features
        features3 = {
            'grid_shape': (5, 5),
            'color_unique_colors': [0, 3, 4],
            'color_num_colors': 3,
            'symmetry_horizontal_symmetry': False
        }
        
        similarity2 = AdvancedSimilarityMetrics.arc_specific_similarity(features1, features3)
        print(f"✓ ARC similarity (different): {similarity2:.3f}")
        
        if similarity > similarity2:
            print("✓ Similarity ranking is correct")
            return True
        else:
            print("✗ Similarity ranking is incorrect")
            return False
            
    except Exception as e:
        print(f"✗ Similarity metrics failed: {e}")
        return False

def test_enhanced_solver_import():
    """Test enhanced solver import."""
    print("\nTesting enhanced solver import...")
    
    try:
        from enhanced_solver import EnhancedARCSolver
        print("✓ Enhanced solver import successful")
        
        # Try creating solver without feature DB
        solver = EnhancedARCSolver()
        print("✓ Enhanced solver creation successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Enhanced solver failed: {e}")
        return False

def main():
    """Run simple tests."""
    print("Simple Retrieval System Test")
    print("=" * 40)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Feature Vectorizer", test_feature_vectorizer),
        ("Similarity Metrics", test_similarity_metrics),
        ("Enhanced Solver Import", test_enhanced_solver_import)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✓ {test_name}: PASSED")
            else:
                print(f"✗ {test_name}: FAILED")
        except Exception as e:
            print(f"✗ {test_name}: ERROR - {e}")
    
    print(f"\n{'='*40}")
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic tests passed!")
    else:
        print("⚠ Some tests failed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
