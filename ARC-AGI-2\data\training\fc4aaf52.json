{"train": [{"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 0, 5, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 0, 5, 5, 5, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 0, 5, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 0, 5, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 0, 5, 5, 5, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 0, 5, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 5, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 5, 0, 5, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 5, 0, 0, 0, 5, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 5, 0, 5, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 5, 0, 5, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 5, 0, 0, 0, 5, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 5, 0, 5, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 5, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}, {"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 1, 2, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 1, 2, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 1, 2, 2, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 1, 2, 2, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 1, 2, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 1, 2, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 2, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 2, 1, 2, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 2, 1, 2, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 2, 1, 1, 2, 2, 8, 8, 8, 8, 8], [8, 2, 1, 1, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 2, 1, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 2, 1, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}], "test": [{"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 4, 4, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 4, 9, 9, 9, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 4, 9, 9, 9, 9, 9, 4, 8, 8, 8, 8, 8, 8, 8, 8], [8, 4, 9, 9, 9, 9, 9, 4, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 4, 9, 9, 9, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 4, 4, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 9, 9, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 4, 4, 4, 9, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 9, 4, 4, 4, 4, 4, 9, 8], [8, 9, 4, 4, 4, 4, 4, 9, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 9, 4, 4, 4, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 9, 9, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}]}