{"train": [{"input": [[1, 1, 2, 2, 3, 3, 4, 4, 5, 5], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 0, 0, 0, 0, 0, 0, 0, 0, 4], [4, 0, 0, 0, 0, 0, 0, 5, 0, 4], [4, 0, 0, 0, 5, 5, 0, 0, 0, 4], [4, 0, 0, 0, 5, 1, 1, 0, 0, 4], [4, 0, 5, 0, 5, 0, 0, 0, 0, 4], [4, 0, 1, 0, 0, 0, 0, 0, 0, 4], [4, 0, 0, 0, 0, 0, 0, 0, 0, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4]], "output": [[1, 1, 2, 2, 3, 3, 4, 4, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 0, 5, 5, 5, 5, 5, 5, 0, 5], [5, 0, 5, 0, 0, 0, 0, 5, 0, 5], [5, 0, 5, 0, 0, 0, 0, 5, 0, 5], [5, 0, 5, 5, 5, 5, 5, 5, 0, 5], [5, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]]}, {"input": [[1, 1, 2, 2, 3, 3, 4, 4, 5, 5], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 0, 0, 3, 3, 3, 0, 0, 4, 2], [2, 0, 3, 3, 0, 3, 3, 4, 3, 2], [2, 0, 3, 0, 3, 0, 4, 3, 3, 2], [2, 0, 3, 4, 0, 0, 0, 3, 4, 2], [2, 0, 3, 0, 3, 3, 3, 4, 0, 2], [2, 0, 3, 3, 3, 4, 3, 3, 3, 2], [2, 0, 0, 0, 0, 0, 0, 0, 4, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "output": [[1, 1, 2, 2, 3, 3, 4, 4, 5, 5], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 0, 3, 3, 3, 3, 3, 3, 3, 3], [3, 0, 3, 0, 0, 0, 0, 0, 3, 3], [3, 0, 3, 0, 0, 0, 0, 0, 3, 3], [3, 0, 3, 0, 0, 0, 0, 0, 3, 3], [3, 0, 3, 0, 0, 0, 0, 0, 3, 3], [3, 0, 3, 3, 3, 3, 3, 3, 3, 3], [3, 0, 0, 0, 0, 0, 0, 0, 0, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]]}, {"input": [[1, 1, 2, 2, 3, 3, 4, 4, 5, 5], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 0, 0, 0, 0, 0, 0, 0, 2, 1], [1, 0, 0, 2, 0, 0, 0, 0, 0, 1], [1, 0, 0, 0, 0, 0, 0, 4, 0, 1], [1, 4, 0, 0, 0, 4, 0, 0, 0, 1], [1, 0, 0, 0, 0, 0, 0, 0, 0, 1], [1, 0, 0, 4, 0, 0, 4, 0, 0, 1], [1, 0, 0, 0, 0, 0, 0, 0, 2, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], "output": [[1, 1, 2, 2, 3, 3, 4, 4, 5, 5], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 0, 0, 0, 0, 0, 0, 0, 0, 4], [4, 0, 0, 0, 0, 0, 0, 0, 0, 4], [4, 4, 4, 4, 4, 4, 4, 4, 0, 4], [4, 4, 0, 0, 0, 0, 0, 4, 0, 4], [4, 4, 0, 0, 0, 0, 0, 4, 0, 4], [4, 4, 4, 4, 4, 4, 4, 4, 0, 4], [4, 0, 0, 0, 0, 0, 0, 0, 0, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4]]}], "test": [{"input": [[1, 1, 2, 2, 3, 3, 4, 4, 5, 5], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 0, 0, 0, 0, 0, 0, 0, 0, 2], [2, 0, 0, 0, 0, 0, 0, 0, 0, 2], [2, 0, 0, 0, 1, 0, 0, 0, 0, 2], [2, 0, 0, 0, 4, 0, 0, 0, 0, 2], [2, 0, 0, 0, 0, 4, 0, 0, 0, 2], [2, 0, 0, 0, 0, 0, 0, 0, 0, 2], [2, 0, 0, 0, 0, 0, 0, 0, 0, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "output": [[1, 1, 2, 2, 3, 3, 4, 4, 5, 5], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 0, 0, 0, 0, 0, 0, 0, 0, 4], [4, 0, 0, 0, 0, 0, 0, 0, 0, 4], [4, 0, 0, 0, 0, 0, 0, 0, 0, 4], [4, 0, 0, 0, 4, 4, 0, 0, 0, 4], [4, 0, 0, 0, 4, 4, 0, 0, 0, 4], [4, 0, 0, 0, 0, 0, 0, 0, 0, 4], [4, 0, 0, 0, 0, 0, 0, 0, 0, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4]]}, {"input": [[1, 1, 2, 2, 3, 3, 4, 4, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 0, 0, 0, 0, 0, 0, 2, 0, 5], [5, 0, 0, 1, 0, 0, 0, 0, 0, 5], [5, 0, 0, 0, 0, 0, 1, 0, 0, 5], [5, 0, 0, 2, 0, 0, 2, 0, 0, 5], [5, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]], "output": [[1, 1, 2, 2, 3, 3, 4, 4, 5, 5], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 0, 0, 0, 0, 0, 0, 0, 0, 2], [2, 0, 0, 2, 2, 2, 2, 2, 0, 2], [2, 0, 0, 2, 0, 0, 0, 2, 0, 2], [2, 0, 0, 2, 0, 0, 0, 2, 0, 2], [2, 0, 0, 2, 2, 2, 2, 2, 0, 2], [2, 0, 0, 0, 0, 0, 0, 0, 0, 2], [2, 0, 0, 0, 0, 0, 0, 0, 0, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2]]}]}