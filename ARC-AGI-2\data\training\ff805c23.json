{"train": [{"input": [[0, 3, 3, 3, 0, 3, 0, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 0, 3, 0, 3, 3, 3, 0], [3, 0, 3, 0, 3, 0, 8, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 8, 0, 3, 0, 3, 0, 3], [3, 3, 3, 3, 3, 3, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 3, 3, 3, 3, 3, 3], [3, 0, 3, 0, 3, 3, 0, 0, 0, 8, 0, 8, 8, 0, 8, 0, 0, 0, 3, 3, 0, 3, 0, 3], [0, 3, 3, 3, 0, 0, 8, 0, 8, 0, 0, 8, 8, 0, 0, 8, 0, 8, 0, 0, 3, 3, 3, 0], [3, 0, 3, 3, 0, 3, 8, 0, 8, 8, 8, 0, 0, 8, 8, 8, 0, 8, 3, 0, 3, 3, 0, 3], [0, 8, 8, 0, 8, 8, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 8, 8, 0, 8, 8, 0], [8, 0, 8, 0, 0, 0, 6, 6, 0, 6, 6, 6, 6, 6, 6, 0, 6, 6, 0, 0, 0, 8, 0, 8], [8, 8, 8, 0, 8, 8, 6, 0, 0, 6, 0, 6, 6, 0, 6, 0, 0, 6, 8, 8, 0, 8, 8, 8], [0, 0, 0, 8, 0, 8, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 8, 0, 8, 0, 0, 0], [8, 0, 8, 0, 0, 8, 6, 6, 0, 6, 6, 6, 6, 6, 6, 0, 6, 6, 8, 0, 0, 8, 0, 8], [8, 0, 8, 8, 8, 0, 1, 1, 1, 1, 1, 0, 0, 6, 6, 6, 6, 6, 0, 8, 8, 8, 0, 8], [8, 0, 8, 8, 8, 0, 1, 1, 1, 1, 1, 0, 0, 6, 6, 6, 6, 6, 0, 8, 8, 8, 0, 8], [8, 0, 8, 0, 0, 8, 1, 1, 1, 1, 1, 6, 6, 6, 6, 0, 6, 6, 8, 0, 0, 8, 0, 8], [0, 0, 0, 8, 0, 8, 1, 1, 1, 1, 1, 6, 6, 6, 6, 6, 6, 6, 8, 0, 8, 0, 0, 0], [8, 8, 8, 0, 8, 8, 1, 1, 1, 1, 1, 6, 6, 0, 6, 0, 0, 6, 8, 8, 0, 8, 8, 8], [8, 0, 8, 0, 0, 0, 6, 6, 0, 6, 6, 6, 6, 6, 6, 0, 6, 6, 0, 0, 0, 8, 0, 8], [0, 8, 8, 0, 8, 8, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 8, 8, 0, 8, 8, 0], [3, 0, 3, 3, 0, 3, 8, 0, 8, 8, 8, 0, 0, 8, 8, 8, 0, 8, 3, 0, 3, 3, 0, 3], [0, 3, 3, 3, 0, 0, 8, 0, 8, 0, 0, 8, 8, 0, 0, 8, 0, 8, 0, 0, 3, 3, 3, 0], [3, 0, 3, 0, 3, 3, 0, 0, 0, 8, 0, 8, 8, 0, 8, 0, 0, 0, 3, 3, 0, 3, 0, 3], [3, 3, 3, 3, 3, 3, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 3, 3, 3, 3, 3, 3], [3, 0, 3, 0, 3, 0, 8, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 8, 0, 3, 0, 3, 0, 3], [0, 3, 3, 3, 0, 3, 0, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 0, 3, 0, 3, 3, 3, 0]], "output": [[6, 6, 6, 6, 6], [6, 6, 6, 6, 6], [6, 6, 0, 6, 6], [6, 6, 6, 6, 6], [6, 0, 0, 6, 0]]}, {"input": [[0, 3, 3, 3, 3, 0, 0, 2, 2, 2, 0, 0, 0, 0, 2, 2, 2, 0, 1, 1, 1, 1, 1, 0], [3, 3, 3, 3, 3, 0, 2, 2, 0, 2, 2, 0, 0, 2, 2, 0, 2, 2, 1, 1, 1, 1, 1, 3], [3, 3, 3, 0, 0, 3, 2, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 2, 1, 1, 1, 1, 1, 3], [3, 3, 0, 3, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 3], [3, 3, 0, 3, 3, 3, 0, 2, 0, 2, 2, 2, 2, 2, 2, 0, 2, 0, 1, 1, 1, 1, 1, 3], [0, 0, 3, 3, 3, 3, 0, 0, 0, 2, 2, 2, 2, 2, 2, 0, 0, 0, 3, 3, 3, 3, 0, 0], [0, 2, 2, 2, 0, 0, 2, 0, 0, 2, 2, 2, 2, 2, 2, 0, 0, 2, 0, 0, 2, 2, 2, 0], [2, 2, 0, 2, 2, 0, 0, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 0, 0, 2, 2, 0, 2, 2], [2, 0, 0, 2, 0, 0, 0, 2, 2, 0, 0, 2, 2, 0, 0, 2, 2, 0, 0, 0, 2, 0, 0, 2], [2, 2, 2, 2, 2, 2, 2, 0, 0, 2, 2, 2, 2, 2, 2, 0, 0, 2, 2, 2, 2, 2, 2, 2], [0, 2, 0, 2, 2, 2, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 2, 2, 2, 0, 2, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0], [0, 2, 0, 2, 2, 2, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 2, 2, 2, 0, 2, 0], [2, 2, 2, 2, 2, 2, 2, 0, 0, 2, 2, 2, 2, 2, 2, 0, 0, 2, 2, 2, 2, 2, 2, 2], [2, 0, 0, 2, 0, 0, 0, 2, 2, 0, 0, 2, 2, 0, 0, 2, 2, 0, 0, 0, 2, 0, 0, 2], [2, 2, 0, 2, 2, 0, 0, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 0, 0, 2, 2, 0, 2, 2], [0, 2, 2, 2, 0, 0, 2, 0, 0, 2, 2, 2, 2, 2, 2, 0, 0, 2, 0, 0, 2, 2, 2, 0], [0, 0, 3, 3, 3, 3, 0, 0, 0, 2, 2, 2, 2, 2, 2, 0, 0, 0, 3, 3, 3, 3, 0, 0], [3, 3, 0, 3, 3, 3, 0, 2, 0, 2, 2, 2, 2, 2, 2, 0, 2, 0, 3, 3, 3, 0, 3, 3], [3, 3, 0, 3, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 0, 3, 3], [3, 3, 3, 0, 0, 3, 2, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 2, 3, 0, 0, 3, 3, 3], [3, 3, 3, 3, 3, 0, 2, 2, 0, 2, 2, 0, 0, 2, 2, 0, 2, 2, 0, 3, 3, 3, 3, 3], [0, 3, 3, 3, 3, 0, 0, 2, 2, 2, 0, 0, 0, 0, 2, 2, 2, 0, 0, 3, 3, 3, 3, 0]], "output": [[0, 3, 3, 3, 3], [0, 3, 3, 3, 3], [3, 0, 0, 3, 3], [3, 3, 3, 0, 3], [3, 3, 3, 0, 3]]}, {"input": [[0, 3, 3, 3, 3, 0, 5, 5, 5, 0, 0, 5, 5, 0, 0, 5, 5, 5, 0, 3, 3, 3, 3, 0], [3, 3, 3, 3, 3, 3, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 3, 3, 3, 3, 3, 3], [3, 3, 3, 0, 0, 0, 5, 0, 0, 5, 5, 0, 0, 5, 5, 0, 0, 5, 0, 0, 0, 3, 3, 3], [3, 3, 0, 0, 3, 3, 0, 0, 5, 0, 5, 5, 5, 5, 0, 5, 0, 0, 3, 3, 0, 0, 3, 3], [3, 3, 0, 3, 3, 0, 0, 0, 5, 5, 0, 0, 0, 0, 5, 5, 0, 0, 0, 3, 3, 0, 3, 3], [0, 3, 0, 3, 0, 3, 5, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 5, 3, 0, 3, 0, 3, 0], [5, 5, 5, 0, 0, 5, 0, 5, 0, 0, 5, 5, 5, 5, 0, 0, 5, 0, 5, 0, 0, 5, 5, 5], [5, 5, 0, 0, 0, 0, 5, 5, 5, 0, 0, 5, 5, 0, 0, 5, 5, 5, 0, 0, 0, 0, 5, 5], [5, 0, 0, 5, 5, 0, 0, 5, 5, 5, 0, 5, 5, 0, 5, 5, 5, 0, 0, 5, 5, 0, 0, 5], [0, 0, 5, 0, 5, 5, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5, 0, 5, 0, 0], [0, 0, 5, 5, 0, 0, 5, 0, 0, 5, 0, 5, 5, 0, 5, 0, 0, 5, 0, 0, 5, 5, 0, 0], [5, 0, 0, 5, 0, 0, 5, 5, 5, 5, 5, 0, 0, 5, 5, 5, 5, 5, 0, 0, 5, 0, 0, 5], [5, 0, 0, 5, 0, 0, 5, 5, 5, 5, 5, 0, 0, 5, 5, 5, 5, 5, 0, 0, 5, 0, 0, 5], [0, 0, 5, 5, 0, 0, 5, 0, 0, 5, 0, 5, 5, 0, 5, 0, 0, 5, 0, 0, 5, 5, 0, 0], [0, 0, 5, 0, 5, 5, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5, 0, 5, 0, 0], [5, 0, 0, 5, 5, 0, 0, 5, 5, 5, 1, 1, 1, 1, 1, 5, 5, 0, 0, 5, 5, 0, 0, 5], [5, 5, 0, 0, 0, 0, 5, 5, 5, 0, 1, 1, 1, 1, 1, 5, 5, 5, 0, 0, 0, 0, 5, 5], [5, 5, 5, 0, 0, 5, 0, 5, 0, 0, 1, 1, 1, 1, 1, 0, 5, 0, 5, 0, 0, 5, 5, 5], [0, 3, 0, 3, 0, 3, 5, 0, 0, 5, 1, 1, 1, 1, 1, 0, 0, 5, 3, 0, 3, 0, 3, 0], [3, 3, 0, 3, 3, 0, 0, 0, 5, 5, 1, 1, 1, 1, 1, 5, 0, 0, 0, 3, 3, 0, 3, 3], [3, 3, 0, 0, 3, 3, 0, 0, 5, 0, 5, 5, 5, 5, 0, 5, 0, 0, 3, 3, 0, 0, 3, 3], [3, 3, 3, 0, 0, 0, 5, 0, 0, 5, 5, 0, 0, 5, 5, 0, 0, 5, 0, 0, 0, 3, 3, 3], [3, 3, 3, 3, 3, 3, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 3, 3, 3, 3, 3, 3], [0, 3, 3, 3, 3, 0, 5, 5, 5, 0, 0, 5, 5, 0, 0, 5, 5, 5, 0, 3, 3, 3, 3, 0]], "output": [[0, 5, 5, 0, 5], [0, 5, 5, 0, 0], [5, 5, 5, 5, 0], [0, 0, 0, 0, 5], [0, 0, 0, 0, 5]]}], "test": [{"input": [[4, 4, 4, 0, 4, 0, 0, 3, 3, 3, 0, 0, 0, 0, 3, 3, 3, 0, 0, 4, 0, 4, 4, 4], [4, 4, 4, 4, 0, 4, 3, 3, 3, 3, 0, 3, 3, 0, 3, 3, 3, 3, 4, 0, 4, 4, 4, 4], [4, 4, 0, 4, 0, 0, 3, 3, 0, 0, 3, 3, 3, 3, 0, 0, 3, 3, 0, 0, 4, 0, 4, 4], [0, 4, 4, 0, 4, 4, 3, 3, 0, 0, 3, 3, 3, 3, 0, 0, 3, 3, 4, 4, 0, 4, 4, 0], [4, 0, 0, 4, 4, 4, 0, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 0, 4, 4, 4, 0, 0, 4], [0, 4, 0, 4, 4, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 4, 4, 0, 4, 0], [0, 3, 3, 3, 0, 0, 8, 8, 8, 1, 1, 1, 1, 1, 8, 8, 8, 8, 0, 0, 3, 3, 3, 0], [3, 3, 3, 3, 0, 3, 8, 8, 8, 1, 1, 1, 1, 1, 0, 8, 8, 8, 3, 0, 3, 3, 3, 3], [3, 3, 0, 0, 3, 3, 8, 8, 8, 1, 1, 1, 1, 1, 0, 8, 8, 8, 3, 3, 0, 0, 3, 3], [3, 3, 0, 0, 3, 3, 8, 0, 0, 1, 1, 1, 1, 1, 8, 0, 0, 8, 3, 3, 0, 0, 3, 3], [0, 0, 3, 3, 0, 3, 8, 0, 8, 1, 1, 1, 1, 1, 8, 8, 0, 8, 3, 0, 3, 3, 0, 0], [0, 3, 3, 3, 3, 3, 8, 8, 0, 8, 8, 8, 8, 8, 8, 0, 8, 8, 3, 3, 3, 3, 3, 0], [0, 3, 3, 3, 3, 3, 8, 8, 0, 8, 8, 8, 8, 8, 8, 0, 8, 8, 3, 3, 3, 3, 3, 0], [0, 0, 3, 3, 0, 3, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 3, 0, 3, 3, 0, 0], [3, 3, 0, 0, 3, 3, 8, 0, 0, 8, 8, 8, 8, 8, 8, 0, 0, 8, 3, 3, 0, 0, 3, 3], [3, 3, 0, 0, 3, 3, 8, 8, 8, 0, 8, 0, 0, 8, 0, 8, 8, 8, 3, 3, 0, 0, 3, 3], [3, 3, 3, 3, 0, 3, 8, 8, 8, 0, 0, 8, 8, 0, 0, 8, 8, 8, 3, 0, 3, 3, 3, 3], [0, 3, 3, 3, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 3, 3, 3, 0], [0, 4, 0, 4, 4, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 4, 4, 0, 4, 0], [4, 0, 0, 4, 4, 4, 0, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 0, 4, 4, 4, 0, 0, 4], [0, 4, 4, 0, 4, 4, 3, 3, 0, 0, 3, 3, 3, 3, 0, 0, 3, 3, 4, 4, 0, 4, 4, 0], [4, 4, 0, 4, 0, 0, 3, 3, 0, 0, 3, 3, 3, 3, 0, 0, 3, 3, 0, 0, 4, 0, 4, 4], [4, 4, 4, 4, 0, 4, 3, 3, 3, 3, 0, 3, 3, 0, 3, 3, 3, 3, 4, 0, 4, 4, 4, 4], [4, 4, 4, 0, 4, 0, 0, 3, 3, 3, 0, 0, 0, 0, 3, 3, 3, 0, 0, 4, 0, 4, 4, 4]], "output": [[8, 8, 8, 8, 8], [0, 0, 8, 8, 0], [0, 8, 0, 0, 8], [8, 8, 8, 8, 8], [8, 0, 8, 8, 0]]}]}