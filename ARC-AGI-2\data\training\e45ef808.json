{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 6, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 6, 6, 1, 1], [6, 1, 1, 1, 1, 1, 1, 6, 6, 6, 6, 1], [6, 6, 1, 1, 1, 1, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 9, 1, 1, 1, 4, 1, 1, 1], [1, 1, 1, 1, 9, 1, 1, 1, 4, 1, 1, 1], [1, 1, 1, 1, 9, 1, 1, 1, 4, 1, 1, 1], [1, 1, 1, 1, 9, 1, 1, 1, 4, 1, 1, 1], [1, 1, 1, 1, 9, 1, 1, 1, 6, 1, 1, 1], [1, 1, 1, 1, 9, 1, 1, 1, 6, 6, 1, 1], [6, 1, 1, 1, 9, 1, 1, 6, 6, 6, 6, 1], [6, 6, 1, 1, 9, 1, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 9, 6, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 6, 1, 1, 1, 6], [1, 1, 1, 1, 1, 1, 6, 6, 6, 1, 6, 6], [1, 1, 6, 1, 1, 6, 6, 6, 6, 6, 6, 6], [1, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], [9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], [9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], [9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], [9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], [9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], [9, 1, 1, 1, 1, 1, 1, 6, 1, 1, 1, 6], [9, 1, 1, 1, 1, 1, 6, 6, 6, 1, 6, 6], [9, 1, 6, 1, 1, 6, 6, 6, 6, 6, 6, 6], [9, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 6, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 6, 6, 6, 1, 1, 1, 1], [1, 6, 6, 1, 6, 6, 6, 6, 6, 6, 1, 1], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 1], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 9], [1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 9], [1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 9], [1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 9], [1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 9], [1, 1, 1, 1, 1, 1, 6, 1, 1, 1, 1, 9], [1, 1, 1, 1, 1, 6, 6, 6, 1, 1, 1, 9], [1, 6, 6, 1, 6, 6, 6, 6, 6, 6, 1, 9], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 9], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6]]}]}