{"train": [{"input": [[4, 7, 7, 7, 4, 7, 7, 7, 7, 6], [4, 7, 7, 4, 4, 4, 7, 7, 7, 6], [4, 7, 7, 7, 7, 7, 7, 7, 7, 6], [4, 7, 7, 7, 7, 6, 7, 7, 7, 6], [4, 7, 7, 7, 4, 6, 6, 6, 7, 6], [4, 7, 7, 7, 4, 7, 7, 7, 7, 6], [4, 7, 7, 6, 6, 7, 7, 7, 7, 6], [4, 7, 4, 6, 7, 7, 7, 6, 7, 6], [4, 7, 4, 4, 7, 7, 6, 6, 7, 6], [4, 7, 4, 4, 4, 7, 7, 7, 7, 6]], "output": [[4, 7, 4, 7, 7, 7, 7, 7, 7, 6], [4, 4, 4, 4, 7, 7, 7, 7, 7, 6], [4, 7, 7, 7, 7, 7, 7, 7, 7, 6], [4, 7, 7, 7, 7, 7, 6, 7, 7, 6], [4, 4, 7, 7, 7, 7, 6, 6, 6, 6], [4, 4, 7, 7, 7, 7, 7, 7, 7, 6], [4, 7, 7, 7, 7, 7, 7, 6, 6, 6], [4, 4, 7, 7, 7, 7, 7, 6, 6, 6], [4, 4, 4, 7, 7, 7, 7, 6, 6, 6], [4, 4, 4, 4, 7, 7, 7, 7, 7, 6]]}, {"input": [[9, 7, 7, 7, 7, 8, 8, 7, 8], [9, 7, 9, 9, 7, 7, 8, 7, 8], [9, 7, 9, 7, 9, 7, 7, 7, 8], [9, 7, 7, 7, 7, 7, 9, 9, 8], [9, 7, 7, 7, 7, 7, 9, 7, 8], [9, 7, 7, 7, 7, 8, 7, 7, 8], [9, 7, 7, 7, 8, 8, 8, 7, 8], [9, 7, 8, 7, 7, 8, 7, 7, 8], [9, 7, 7, 7, 7, 7, 7, 7, 8]], "output": [[9, 7, 7, 7, 7, 7, 8, 8, 8], [9, 9, 9, 7, 7, 7, 7, 8, 8], [9, 9, 7, 9, 7, 7, 7, 7, 8], [9, 9, 9, 7, 7, 7, 7, 7, 8], [9, 9, 7, 7, 7, 7, 7, 7, 8], [9, 7, 7, 7, 7, 7, 8, 7, 8], [9, 7, 7, 7, 7, 8, 8, 8, 8], [9, 7, 7, 7, 7, 8, 8, 7, 8], [9, 7, 7, 7, 7, 7, 7, 7, 8]]}], "test": [{"input": [[8, 7, 7, 7, 7, 7, 7, 7, 1], [8, 7, 1, 1, 7, 7, 7, 7, 1], [8, 7, 1, 7, 7, 7, 7, 7, 1], [8, 7, 7, 7, 7, 1, 1, 7, 1], [8, 7, 8, 7, 1, 1, 1, 7, 1], [8, 7, 8, 8, 7, 7, 7, 8, 1], [8, 7, 8, 8, 7, 1, 1, 7, 1], [8, 7, 8, 7, 1, 1, 1, 7, 1], [8, 7, 8, 7, 7, 1, 1, 7, 1]], "output": [[8, 7, 7, 7, 7, 7, 7, 7, 1], [8, 7, 7, 7, 7, 7, 1, 1, 1], [8, 7, 7, 7, 7, 7, 1, 7, 1], [8, 7, 7, 7, 7, 7, 1, 1, 1], [8, 8, 7, 7, 7, 1, 1, 1, 1], [8, 8, 8, 8, 7, 7, 7, 7, 1], [8, 8, 8, 7, 7, 7, 1, 1, 1], [8, 8, 7, 7, 7, 1, 1, 1, 1], [8, 8, 7, 7, 7, 7, 1, 1, 1]]}]}