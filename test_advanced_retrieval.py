#!/usr/bin/env python3
"""
Test Advanced Retrieval System

This script tests the advanced retrieval system and compares it with
the basic retrieval approach.
"""

import sys
import time
import numpy as np
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from arc_large_scale_evaluation import ARCDatasetLoader, LargeScaleEvaluator
from arc_solver_components import HybridARCSolver, build_feature_database
from enhanced_solver import EnhancedARCSolver
from advanced_retrieval import AdvancedRetrievalSystem, AdvancedFeatureVectorizer

def test_feature_vectorization():
    """Test the advanced feature vectorization system."""
    print("Testing Advanced Feature Vectorization")
    print("=" * 50)
    
    # Load some sample data
    loader = ARCDatasetLoader()
    training_data = loader.load_training_dataset(max_puzzles=5, shuffle=False)
    
    if len(training_data) < 3:
        print("✗ Not enough training data")
        return False
    
    # Build feature database
    feature_db = build_feature_database(training_data, max_puzzles=5)
    
    # Test vectorizer
    vectorizer = AdvancedFeatureVectorizer()
    
    # Collect features from examples
    all_features = []
    for puzzle_data in feature_db['puzzles'].values():
        for example in puzzle_data['train_examples']:
            all_features.append(example['input_features'])
    
    if len(all_features) < 3:
        print("✗ Not enough feature examples")
        return False
    
    try:
        # Test fit_transform
        feature_vectors = vectorizer.fit_transform(all_features)
        print(f"✓ Vectorized {len(all_features)} feature sets")
        print(f"  Feature vector dimension: {feature_vectors.shape[1]}")
        
        # Test transform on new features
        test_features = all_features[0]
        test_vector = vectorizer.transform(test_features)
        print(f"✓ Single feature transformation works: {test_vector.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Feature vectorization failed: {e}")
        return False

def test_similarity_metrics():
    """Test the advanced similarity metrics."""
    print("\nTesting Advanced Similarity Metrics")
    print("=" * 50)
    
    from advanced_retrieval import AdvancedSimilarityMetrics
    
    # Create test feature dictionaries
    features1 = {
        'grid_shape': (3, 3),
        'color_unique_colors': [0, 1, 2],
        'color_num_colors': 3,
        'symmetry_horizontal_symmetry': True,
        'symmetry_vertical_symmetry': False,
        'shape_num_rectangles': 1,
        'shape_num_lines': 0
    }
    
    features2 = {
        'grid_shape': (3, 3),
        'color_unique_colors': [0, 1, 2],
        'color_num_colors': 3,
        'symmetry_horizontal_symmetry': True,
        'symmetry_vertical_symmetry': False,
        'shape_num_rectangles': 1,
        'shape_num_lines': 0
    }
    
    features3 = {
        'grid_shape': (5, 5),
        'color_unique_colors': [0, 3, 4, 5],
        'color_num_colors': 4,
        'symmetry_horizontal_symmetry': False,
        'symmetry_vertical_symmetry': True,
        'shape_num_rectangles': 0,
        'shape_num_lines': 2
    }
    
    try:
        # Test ARC-specific similarity
        sim_identical = AdvancedSimilarityMetrics.arc_specific_similarity(features1, features2)
        sim_different = AdvancedSimilarityMetrics.arc_specific_similarity(features1, features3)
        
        print(f"✓ Similarity between identical features: {sim_identical:.3f}")
        print(f"✓ Similarity between different features: {sim_different:.3f}")
        
        if sim_identical > sim_different:
            print("✓ Similarity metric correctly ranks identical > different")
            return True
        else:
            print("✗ Similarity metric ranking is incorrect")
            return False
            
    except Exception as e:
        print(f"✗ Similarity metrics failed: {e}")
        return False

def test_retrieval_system():
    """Test the advanced retrieval system."""
    print("\nTesting Advanced Retrieval System")
    print("=" * 50)
    
    # Load data and build feature database
    loader = ARCDatasetLoader()
    training_data = loader.load_training_dataset(max_puzzles=10, shuffle=False)
    
    if len(training_data) < 5:
        print("✗ Not enough training data")
        return False
    
    feature_db = build_feature_database(training_data, max_puzzles=10)
    
    try:
        # Create advanced retrieval system
        retrieval_system = AdvancedRetrievalSystem(feature_db)
        
        # Test with a query from the database
        puzzle_id = list(feature_db['puzzles'].keys())[0]
        query_example = feature_db['puzzles'][puzzle_id]['train_examples'][0]
        query_features = query_example['input_features']
        
        # Find similar examples
        similar_examples = retrieval_system.find_similar_examples(query_features, k=5)
        
        print(f"✓ Found {len(similar_examples)} similar examples")
        
        if similar_examples:
            print(f"  Top similarity score: {similar_examples[0]['similarity']:.3f}")
            print(f"  Lowest similarity score: {similar_examples[-1]['similarity']:.3f}")
            
            # Test transformation pattern extraction
            patterns = retrieval_system.get_transformation_patterns(similar_examples[:3])
            print(f"✓ Extracted {len(patterns)} transformation patterns")
            
            if patterns:
                print(f"  Sample pattern confidence: {patterns[0]['confidence']:.3f}")
        
        # Find similar puzzles
        similar_puzzles = retrieval_system.find_similar_puzzles(query_features, k=3)
        print(f"✓ Found {len(similar_puzzles)} similar puzzles")
        
        if similar_puzzles:
            print(f"  Top puzzle similarity: {similar_puzzles[0]['similarity']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Retrieval system failed: {e}")
        return False

def test_enhanced_solver_with_retrieval():
    """Test the enhanced solver with advanced retrieval."""
    print("\nTesting Enhanced Solver with Advanced Retrieval")
    print("=" * 50)
    
    # Load data
    loader = ARCDatasetLoader()
    training_data = loader.load_training_dataset(max_puzzles=15, shuffle=False)
    
    if len(training_data) < 10:
        print("✗ Not enough training data")
        return False
    
    # Split data
    all_puzzle_ids = list(training_data.keys())
    feature_puzzle_ids = all_puzzle_ids[:8]
    test_puzzle_ids = all_puzzle_ids[8:12]
    
    feature_data = {pid: training_data[pid] for pid in feature_puzzle_ids}
    test_data = {pid: training_data[pid] for pid in test_puzzle_ids}
    
    # Build feature database
    feature_db = build_feature_database(feature_data, max_puzzles=8)
    
    try:
        # Create enhanced solver
        enhanced_solver = EnhancedARCSolver(
            feature_db=feature_db,
            max_retrieval_candidates=5,
            max_transformation_depth=2
        )
        
        # Test on one puzzle
        test_puzzle_id = test_puzzle_ids[0]
        test_puzzle_data = test_data[test_puzzle_id]
        
        if not test_puzzle_data.get('test'):
            print("✗ No test examples in puzzle")
            return False
        
        test_input = np.array(test_puzzle_data['test'][0]['input'])
        training_examples = [
            {
                'input': np.array(ex['input']),
                'output': np.array(ex['output'])
            }
            for ex in test_puzzle_data.get('train', [])
        ]
        
        # Solve with enhanced retrieval
        solution = enhanced_solver.solve_puzzle_enhanced(
            test_input=test_input,
            training_examples=training_examples,
            use_retrieval=True,
            use_heuristics=True,
            use_smart_search=True
        )
        
        print(f"✓ Enhanced solver completed")
        print(f"  Method: {solution['method']}")
        print(f"  Confidence: {solution['confidence']:.3f}")
        print(f"  Processing time: {solution['processing_time']:.3f}s")
        print(f"  Retrieval results: {len(solution['retrieval_results'])}")
        print(f"  Total candidates: {len(solution['candidates'])}")
        
        # Check if retrieval was actually used
        retrieval_used = any('retrieval' in candidate.get('method', '') 
                           for candidate in solution['candidates'])
        
        if retrieval_used:
            print("✓ Advanced retrieval was successfully used")
            return True
        else:
            print("~ Enhanced solver worked but didn't use retrieval")
            return True
            
    except Exception as e:
        print(f"✗ Enhanced solver with retrieval failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Advanced Retrieval System Test Suite")
    print("=" * 60)
    
    tests = [
        ("Feature Vectorization", test_feature_vectorization),
        ("Similarity Metrics", test_similarity_metrics),
        ("Retrieval System", test_retrieval_system),
        ("Enhanced Solver with Retrieval", test_enhanced_solver_with_retrieval)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        try:
            result = test_func()
            if result:
                print(f"✓ {test_name}: PASSED")
                passed += 1
            else:
                print(f"✗ {test_name}: FAILED")
        except Exception as e:
            print(f"✗ {test_name}: ERROR - {e}")
    
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print("="*60)
    print(f"Passed: {passed}/{total} tests")
    
    if passed == total:
        print("\n🎉 All tests passed! Advanced retrieval system is ready.")
        print("\nNext steps:")
        print("1. Run large-scale evaluation with enhanced retrieval")
        print("2. Fine-tune similarity metrics and feature weights")
        print("3. Add more sophisticated transformation pattern recognition")
    elif passed >= total * 0.75:
        print(f"\n~ Most tests passed. System is mostly functional.")
        print("Consider addressing failed tests for optimal performance.")
    else:
        print(f"\n⚠ Multiple test failures. Please fix issues before deployment.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
