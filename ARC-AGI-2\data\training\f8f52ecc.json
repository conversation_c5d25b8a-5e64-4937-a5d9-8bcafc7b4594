{"train": [{"input": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 2, 1, 1, 1, 1, 1], [1, 1, 1, 1, 2, 1, 1, 1, 1, 1], [1, 1, 8, 8, 8, 8, 8, 8, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 2, 1, 2, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], "output": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 2, 1, 1, 1, 1, 1], [1, 2, 2, 2, 2, 1, 1, 1, 1, 1], [1, 2, 8, 8, 8, 8, 8, 8, 1, 1], [1, 2, 1, 1, 1, 1, 1, 1, 1, 1], [1, 2, 2, 2, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]]}, {"input": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 2, 1, 1, 1, 1, 4, 4, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 2, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 4, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 2, 2, 2, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 8, 1, 1, 1, 1, 1, 3, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], "output": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 2, 1, 1, 1, 1, 4, 4, 1], [1, 1, 2, 1, 1, 1, 1, 1, 4, 1], [1, 1, 2, 1, 1, 1, 1, 1, 4, 1], [1, 1, 2, 1, 1, 1, 1, 1, 4, 1], [1, 1, 2, 1, 1, 1, 1, 1, 1, 1], [1, 1, 2, 2, 2, 2, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 8, 1, 1, 1, 1, 1, 3, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]]}, {"input": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 9, 1, 9, 1, 1, 1, 1, 1, 1], [1, 1, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2], [1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 1, 1], [1, 1, 1, 1, 1, 6, 1, 3, 1, 1, 1, 2], [1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 1, 1], [1, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 6, 2, 1, 1, 1, 1, 1, 1, 1, 1], [5, 1, 1, 1, 7, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 4, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1]], "output": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 9, 9, 9, 9, 1, 1, 1, 1, 1, 1], [1, 1, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2], [1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 1, 2], [1, 6, 6, 6, 6, 6, 1, 3, 1, 1, 1, 2], [1, 6, 8, 8, 8, 8, 8, 8, 8, 8, 1, 2], [1, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2], [1, 6, 6, 2, 2, 2, 2, 2, 2, 2, 2, 2], [5, 1, 1, 1, 7, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 1]]}], "test": [{"input": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 2, 1, 1, 2, 1, 1, 1, 4, 1], [1, 1, 1, 1, 1, 8, 1, 1, 1, 1], [1, 1, 1, 1, 2, 1, 2, 1, 1, 1], [0, 1, 1, 1, 1, 1, 1, 1, 8, 8], [1, 1, 5, 1, 5, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 7, 1], [1, 3, 1, 1, 1, 1, 1, 1, 1, 1]], "output": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 2, 2, 2, 2, 1, 1, 1, 4, 1], [1, 1, 1, 1, 2, 8, 1, 1, 1, 1], [1, 1, 1, 1, 2, 2, 2, 1, 1, 1], [0, 1, 1, 1, 1, 1, 1, 1, 8, 8], [1, 1, 5, 5, 5, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 7, 1], [1, 3, 1, 1, 1, 1, 1, 1, 1, 1]]}, {"input": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 4, 1, 1, 1, 1, 4, 1, 1, 1, 7, 1], [1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 1], [1, 1, 1, 1, 1, 1, 1, 1, 3, 1, 1, 1], [1, 1, 1, 6, 1, 1, 1, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1], [1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 3, 1, 3, 1, 1, 1], [1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 2, 2, 1, 1, 1, 1, 9, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], "output": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 4, 4, 4, 4, 4, 4, 1, 1, 1, 7, 1], [1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 1], [1, 1, 1, 1, 1, 1, 3, 3, 3, 1, 1, 1], [1, 1, 1, 6, 1, 1, 3, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1], [1, 2, 2, 1, 1, 1, 3, 1, 1, 1, 1, 1], [1, 1, 2, 1, 1, 1, 3, 3, 3, 1, 1, 1], [1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 2, 2, 2, 2, 1, 1, 1, 1, 9, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]]}]}