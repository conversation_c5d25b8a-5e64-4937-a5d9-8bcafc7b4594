{"train": [{"input": [[2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 3, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 1, 3, 1, 1, 2, 2], [2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 8, 2, 2], [2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "output": [[2, 2, 1, 2, 2], [2, 2, 1, 2, 2], [1, 1, 1, 1, 1], [2, 2, 1, 2, 2], [2, 2, 3, 2, 2], [2, 1, 3, 1, 1], [2, 1, 2, 2, 8], [2, 1, 1, 1, 1]]}, {"input": [[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 1, 3, 1, 2, 2, 2, 2, 2, 2, 2, 2], [2, 1, 1, 2, 1, 1, 2, 2, 2, 2, 2, 2, 2], [2, 1, 2, 2, 2, 1, 2, 1, 1, 1, 2, 2, 2], [2, 1, 1, 2, 1, 1, 2, 8, 2, 1, 2, 2, 2], [2, 2, 1, 1, 1, 2, 2, 1, 3, 1, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 8, 2, 2, 2], [2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "output": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 2], [2, 2, 2, 2, 2, 8, 8, 2, 1, 2], [1, 1, 1, 1, 1, 1, 1, 3, 1, 2], [2, 2, 2, 2, 2, 2, 1, 3, 1, 2], [2, 2, 2, 2, 2, 1, 1, 2, 1, 1], [2, 2, 2, 2, 2, 1, 2, 2, 2, 1], [2, 2, 2, 2, 2, 1, 1, 2, 1, 1], [2, 2, 2, 2, 2, 2, 1, 1, 1, 2]]}, {"input": [[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 1, 1, 1, 2, 2, 2, 2, 1, 8, 1, 2, 2], [2, 1, 2, 4, 2, 2, 2, 2, 1, 2, 1, 2, 2], [2, 1, 1, 1, 2, 2, 2, 2, 1, 2, 1, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 1, 2, 2], [2, 2, 2, 1, 1, 1, 2, 2, 1, 3, 1, 2, 2], [2, 2, 2, 4, 2, 1, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 1, 8, 1, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "output": [[1, 1, 1, 1, 1, 1], [1, 2, 4, 4, 2, 1], [1, 1, 1, 1, 8, 1], [2, 2, 2, 1, 8, 1], [2, 2, 2, 1, 2, 1], [2, 2, 2, 1, 2, 1], [2, 2, 2, 1, 2, 1], [2, 2, 2, 1, 3, 1]]}], "test": [{"input": [[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 1, 3, 1, 1, 2, 2, 2, 2, 1, 3, 1, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 1, 2], [2, 2, 2, 2, 1, 2, 1, 2, 2, 8, 2, 1, 2], [2, 2, 2, 2, 1, 2, 8, 2, 2, 1, 2, 1, 2], [2, 2, 2, 2, 1, 1, 1, 2, 2, 1, 2, 1, 2], [2, 2, 2, 2, 2, 1, 2, 2, 2, 1, 2, 1, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 4, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "output": [[2, 2, 2, 1, 1, 1, 1], [2, 2, 2, 2, 1, 2, 2], [2, 2, 2, 1, 3, 1, 1], [2, 2, 2, 1, 3, 1, 2], [1, 2, 1, 1, 2, 1, 2], [1, 2, 8, 8, 2, 1, 2], [1, 1, 1, 1, 2, 1, 2], [2, 1, 2, 1, 2, 1, 2], [2, 2, 2, 1, 2, 1, 2], [2, 2, 2, 1, 2, 4, 2], [2, 2, 2, 1, 1, 1, 2]]}, {"input": [[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 1, 2, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2], [2, 1, 1, 1, 4, 2, 2, 2, 1, 1, 8, 1, 2], [1, 3, 1, 1, 1, 2, 2, 2, 2, 1, 1, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 2, 2], [2, 2, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2], [2, 2, 4, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2], [2, 2, 1, 1, 8, 1, 1, 2, 2, 1, 3, 1, 2], [2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 2], [2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 1, 2], [2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 1, 2], [2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 1, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "output": [[2, 2, 1, 2, 1, 1, 1, 1, 1, 1, 1], [2, 2, 1, 1, 1, 4, 4, 2, 2, 2, 1], [2, 1, 3, 1, 1, 1, 1, 1, 8, 1, 1], [2, 1, 3, 1, 2, 2, 1, 1, 8, 1, 2], [1, 1, 1, 1, 2, 2, 2, 1, 1, 2, 2], [1, 2, 2, 1, 2, 2, 2, 1, 1, 2, 2], [1, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2], [1, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2]]}]}