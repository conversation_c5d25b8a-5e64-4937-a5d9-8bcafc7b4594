{"train": [{"input": [[7, 7, 6, 7, 7, 6, 7, 6, 7, 7, 7, 6], [7, 8, 7, 7, 6, 7, 7, 8, 6, 7, 8, 7], [7, 7, 7, 6, 7, 7, 7, 7, 6, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 8, 7, 7, 8, 6, 7, 8, 7, 7, 8, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [6, 7, 7, 7, 7, 7, 7, 7, 6, 7, 7, 7], [7, 6, 7, 7, 8, 7, 6, 6, 6, 7, 8, 7], [7, 6, 7, 7, 7, 7, 6, 6, 7, 7, 7, 6], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 8, 7, 7, 8, 7, 6, 8, 7, 7, 8, 6], [7, 7, 7, 7, 7, 7, 6, 7, 7, 6, 7, 7]], "output": [[7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 8, 7, 7, 8, 7, 7, 8, 7, 7, 8, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 8, 7, 7, 8, 7, 7, 8, 7, 7, 8, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 8, 7, 7, 8, 7, 7, 8, 7, 7, 8, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 8, 7, 7, 8, 7, 7, 8, 7, 7, 8, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7]]}, {"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 6, 6, 6, 8, 8, 8, 8, 8], [8, 4, 8, 4, 8, 4, 8, 4, 6, 4, 8, 4, 8, 4, 6, 4, 8, 4, 8], [6, 8, 8, 6, 8, 6, 8, 8, 8, 8, 8, 8, 6, 6, 8, 8, 6, 8, 8], [8, 4, 8, 4, 8, 6, 6, 4, 8, 4, 6, 4, 8, 4, 8, 6, 8, 4, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6], [8, 4, 8, 4, 8, 4, 6, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8], [8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 8, 8, 6, 8, 6], [8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 6, 4, 8, 4, 6, 4, 8], [8, 8, 8, 8, 6, 8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 6, 8, 4, 8, 4, 6, 4, 6, 6, 8, 4, 8, 4, 8, 4, 8, 4, 8], [8, 8, 8, 6, 8, 6, 6, 8, 6, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8], [8, 4, 8, 4, 6, 6, 6, 4, 8, 4, 6, 4, 8, 4, 8, 4, 8, 4, 8], [8, 8, 8, 8, 8, 6, 8, 8, 6, 8, 8, 6, 8, 6, 8, 8, 8, 8, 8], [8, 4, 8, 6, 8, 6, 8, 4, 8, 4, 8, 4, 6, 4, 8, 4, 6, 4, 8], [8, 6, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8, 4, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}], "test": [{"input": [[3, 6, 3, 3, 3, 3, 3, 3, 6, 6, 3, 3, 3, 3, 3, 3], [3, 1, 1, 3, 6, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3], [6, 1, 1, 3, 6, 1, 3, 1, 1, 3, 1, 6, 3, 1, 6, 3], [6, 6, 3, 3, 6, 6, 6, 3, 6, 3, 3, 3, 6, 3, 6, 3], [3, 1, 1, 6, 1, 1, 3, 1, 1, 3, 1, 1, 3, 6, 1, 6], [3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 6, 1, 3, 1, 1, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 6, 3, 3, 3, 3], [3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 6, 1, 6, 1, 1, 3], [3, 1, 6, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 6], [6, 6, 1, 3, 6, 6, 3, 6, 1, 3, 6, 6, 3, 1, 1, 6], [6, 1, 1, 3, 6, 6, 3, 6, 1, 3, 1, 6, 3, 6, 1, 6], [3, 3, 6, 3, 6, 3, 6, 3, 3, 3, 6, 3, 3, 3, 3, 6], [3, 1, 1, 3, 1, 6, 3, 1, 6, 3, 6, 1, 3, 1, 1, 6], [6, 6, 1, 6, 1, 1, 3, 6, 1, 3, 6, 6, 3, 6, 1, 6], [3, 3, 3, 3, 3, 6, 3, 3, 3, 6, 3, 6, 6, 3, 3, 3]], "output": [[3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3], [3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3], [3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3], [3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3], [3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3], [3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1, 1, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]]}]}