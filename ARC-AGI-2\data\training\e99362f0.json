{"train": [{"input": [[0, 7, 0, 0, 4, 9, 0, 9, 0], [7, 7, 0, 0, 4, 9, 0, 0, 0], [0, 0, 0, 0, 4, 9, 0, 9, 9], [0, 7, 7, 7, 4, 0, 0, 0, 0], [0, 0, 7, 7, 4, 0, 0, 9, 9], [4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 2, 2, 0, 4, 8, 8, 0, 0], [2, 2, 0, 2, 4, 8, 0, 8, 8], [2, 0, 2, 2, 4, 0, 8, 0, 8], [2, 0, 2, 2, 4, 0, 8, 8, 0], [2, 0, 0, 0, 4, 0, 0, 8, 0]], "output": [[8, 8, 9, 0], [8, 7, 8, 8], [9, 8, 9, 8], [2, 8, 8, 7], [2, 0, 8, 7]]}, {"input": [[0, 7, 7, 0, 4, 0, 0, 0, 0], [0, 0, 0, 0, 4, 0, 9, 0, 9], [0, 7, 7, 0, 4, 9, 9, 0, 9], [7, 0, 7, 7, 4, 0, 0, 0, 9], [7, 0, 7, 7, 4, 9, 0, 0, 9], [4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 2, 2, 4, 8, 8, 8, 0], [0, 2, 0, 2, 4, 0, 0, 0, 8], [2, 2, 2, 2, 4, 0, 0, 8, 8], [0, 0, 2, 2, 4, 8, 0, 0, 0], [0, 0, 2, 0, 4, 0, 8, 8, 0]], "output": [[8, 8, 8, 2], [0, 9, 0, 8], [9, 7, 8, 8], [8, 0, 7, 7], [7, 8, 8, 7]]}, {"input": [[7, 7, 7, 0, 4, 9, 0, 0, 0], [7, 7, 7, 7, 4, 0, 9, 0, 9], [7, 7, 7, 7, 4, 0, 0, 9, 0], [0, 7, 0, 7, 4, 9, 9, 9, 9], [7, 7, 0, 7, 4, 9, 0, 0, 9], [4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 2, 0, 2, 4, 0, 0, 0, 8], [2, 2, 2, 0, 4, 0, 8, 0, 0], [2, 0, 2, 2, 4, 0, 0, 0, 8], [0, 0, 2, 2, 4, 0, 8, 0, 0], [0, 2, 2, 0, 4, 8, 8, 0, 0]], "output": [[7, 7, 7, 8], [7, 8, 7, 7], [7, 7, 7, 8], [9, 8, 9, 7], [8, 8, 2, 7]]}, {"input": [[0, 0, 0, 0, 4, 0, 9, 0, 0], [7, 0, 7, 7, 4, 9, 9, 9, 9], [7, 0, 7, 7, 4, 9, 9, 0, 0], [7, 7, 0, 0, 4, 0, 0, 9, 0], [7, 0, 0, 7, 4, 9, 9, 9, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 2, 2, 2, 4, 8, 0, 0, 0], [2, 2, 2, 2, 4, 8, 8, 8, 8], [2, 0, 0, 2, 4, 8, 8, 8, 0], [2, 2, 0, 0, 4, 0, 8, 8, 8], [2, 2, 2, 0, 4, 0, 8, 8, 0]], "output": [[8, 9, 2, 2], [8, 8, 8, 8], [8, 8, 8, 7], [7, 8, 8, 8], [7, 8, 8, 7]]}, {"input": [[7, 0, 0, 0, 4, 0, 0, 9, 0], [7, 7, 0, 0, 4, 9, 9, 0, 9], [0, 0, 0, 0, 4, 0, 9, 9, 0], [0, 0, 7, 0, 4, 0, 0, 0, 0], [7, 0, 7, 7, 4, 9, 0, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4], [2, 0, 2, 0, 4, 0, 0, 0, 0], [2, 0, 0, 2, 4, 0, 0, 8, 8], [2, 0, 0, 2, 4, 8, 0, 0, 8], [0, 0, 0, 2, 4, 0, 8, 0, 0], [0, 0, 0, 0, 4, 0, 0, 8, 8]], "output": [[7, 0, 9, 0], [7, 7, 8, 8], [8, 9, 9, 8], [0, 8, 7, 2], [7, 0, 8, 8]]}, {"input": [[7, 0, 7, 7, 4, 0, 9, 9, 9], [0, 7, 7, 0, 4, 9, 9, 9, 0], [0, 0, 0, 0, 4, 9, 0, 0, 0], [7, 0, 0, 7, 4, 9, 9, 9, 0], [7, 0, 7, 7, 4, 9, 0, 9, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 2, 0, 0, 4, 0, 0, 8, 0], [2, 0, 2, 2, 4, 8, 0, 8, 8], [0, 2, 0, 0, 4, 0, 0, 8, 8], [2, 0, 2, 2, 4, 8, 0, 0, 8], [2, 2, 2, 0, 4, 8, 8, 0, 0]], "output": [[7, 9, 8, 7], [8, 7, 8, 8], [9, 2, 8, 8], [8, 9, 9, 8], [8, 8, 7, 7]]}], "test": [{"input": [[7, 7, 0, 0, 4, 0, 9, 9, 0], [7, 0, 0, 0, 4, 0, 9, 0, 9], [0, 7, 7, 0, 4, 9, 9, 9, 9], [7, 7, 0, 0, 4, 9, 0, 9, 9], [7, 0, 0, 0, 4, 9, 9, 0, 9], [4, 4, 4, 4, 4, 4, 4, 4, 4], [2, 2, 0, 2, 4, 8, 8, 0, 8], [0, 0, 2, 0, 4, 8, 8, 0, 0], [0, 0, 2, 0, 4, 8, 0, 8, 8], [0, 0, 0, 2, 4, 8, 8, 8, 0], [0, 0, 2, 2, 4, 8, 8, 8, 0]], "output": [[8, 8, 9, 8], [8, 8, 2, 9], [8, 7, 8, 8], [8, 8, 8, 9], [8, 8, 8, 9]]}]}