{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0], [0, 0, 0, 0, 2, 0, 3, 5, 0, 2, 0, 0, 0, 0], [0, 0, 0, 0, 2, 0, 6, 8, 0, 2, 0, 0, 0, 0], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0], [0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[2, 2, 2, 2, 2, 2], [2, 3, 3, 5, 5, 2], [2, 3, 3, 5, 5, 2], [2, 6, 6, 8, 8, 2], [2, 6, 6, 8, 8, 2], [2, 2, 2, 2, 2, 2]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 2, 0, 3, 3, 4, 4, 0, 0, 0, 2, 0, 0], [0, 0, 2, 0, 3, 3, 4, 4, 0, 0, 0, 2, 0, 0], [0, 0, 2, 0, 1, 1, 8, 8, 0, 0, 0, 2, 0, 0], [0, 0, 2, 0, 1, 1, 8, 8, 0, 0, 0, 2, 0, 0], [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 3, 3, 3, 3, 4, 4, 4, 4, 2], [2, 3, 3, 3, 3, 4, 4, 4, 4, 2], [2, 3, 3, 3, 3, 4, 4, 4, 4, 2], [2, 3, 3, 3, 3, 4, 4, 4, 4, 2], [2, 1, 1, 1, 1, 8, 8, 8, 8, 2], [2, 1, 1, 1, 1, 8, 8, 8, 8, 2], [2, 1, 1, 1, 1, 8, 8, 8, 8, 2], [2, 1, 1, 1, 1, 8, 8, 8, 8, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0], [0, 2, 0, 3, 3, 6, 6, 0, 0, 0, 2, 0, 0, 0], [0, 2, 0, 3, 3, 6, 6, 0, 0, 0, 2, 0, 0, 0], [0, 2, 0, 4, 4, 1, 1, 0, 0, 0, 2, 0, 0, 0], [0, 2, 0, 4, 4, 1, 1, 0, 0, 0, 2, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0]], "output": [[2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 3, 3, 3, 3, 6, 6, 6, 6, 2], [2, 3, 3, 3, 3, 6, 6, 6, 6, 2], [2, 3, 3, 3, 3, 6, 6, 6, 6, 2], [2, 3, 3, 3, 3, 6, 6, 6, 6, 2], [2, 4, 4, 4, 4, 1, 1, 1, 1, 2], [2, 4, 4, 4, 4, 1, 1, 1, 1, 2], [2, 4, 4, 4, 4, 1, 1, 1, 1, 2], [2, 4, 4, 4, 4, 1, 1, 1, 1, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2]]}]}