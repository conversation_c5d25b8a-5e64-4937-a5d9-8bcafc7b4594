#!/usr/bin/env python3
"""
ARC-AGI-2 Solver Components

This module contains all the core components extracted from the notebook
for use in large-scale evaluation and deployment.

Components:
- Feature extraction functions
- Transformation classes
- Solver classes
- Evaluation utilities
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
import itertools
from collections import Counter, defaultdict
import copy
import time
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.neighbors import NearestNeighbors
from scipy import ndimage
from scipy.ndimage import label, binary_erosion, binary_dilation
import warnings
warnings.filterwarnings('ignore')

# ============================================================================
# FEATURE EXTRACTION FUNCTIONS
# ============================================================================

def extract_color_features(grid: np.ndarray) -> Dict[str, Any]:
    """
    Extract color-based features from a grid.
    
    Args:
        grid: 2D numpy array representing the grid
        
    Returns:
        Dictionary containing color features
    """
    grid = np.array(grid)
    flat_grid = grid.flatten()
    
    # Basic color statistics
    unique_colors = np.unique(flat_grid)
    color_counts = Counter(flat_grid)
    total_cells = len(flat_grid)
    
    features = {
        # Basic counts
        'num_colors': len(unique_colors),
        'unique_colors': sorted(unique_colors.tolist()),
        'total_cells': total_cells,
        
        # Color histogram (normalized)
        'color_histogram': {int(color): count/total_cells for color, count in color_counts.items()},
        
        # Dominant colors
        'most_common_color': color_counts.most_common(1)[0][0],
        'most_common_color_ratio': color_counts.most_common(1)[0][1] / total_cells,
        
        # Background detection (assume most common color is background)
        'background_color': color_counts.most_common(1)[0][0],
        'background_ratio': color_counts.most_common(1)[0][1] / total_cells,
        
        # Non-background colors
        'foreground_colors': [color for color in unique_colors 
                            if color != color_counts.most_common(1)[0][0]],
        'num_foreground_colors': len(unique_colors) - 1 if len(unique_colors) > 1 else 0,
    }
    
    # Color diversity metrics
    if len(unique_colors) > 1:
        # Shannon entropy for color diversity
        probabilities = np.array(list(color_counts.values())) / total_cells
        features['color_entropy'] = -np.sum(probabilities * np.log2(probabilities + 1e-10))
        
        # Gini coefficient for color concentration
        sorted_counts = sorted(color_counts.values())
        n = len(sorted_counts)
        cumsum = np.cumsum(sorted_counts)
        features['color_gini'] = (n + 1 - 2 * np.sum(cumsum) / cumsum[-1]) / n
    else:
        features['color_entropy'] = 0.0
        features['color_gini'] = 0.0
    
    return features

def extract_color_pattern_features(grid: np.ndarray) -> Dict[str, Any]:
    """
    Extract pattern-based color features.
    
    Args:
        grid: 2D numpy array representing the grid
        
    Returns:
        Dictionary containing color pattern features
    """
    grid = np.array(grid)
    features = {}
    
    # Color transitions (how often colors change)
    horizontal_transitions = 0
    vertical_transitions = 0
    
    # Horizontal transitions
    for row in grid:
        for i in range(len(row) - 1):
            if row[i] != row[i + 1]:
                horizontal_transitions += 1
    
    # Vertical transitions
    for col in range(grid.shape[1]):
        for row in range(grid.shape[0] - 1):
            if grid[row, col] != grid[row + 1, col]:
                vertical_transitions += 1
    
    total_possible_h = grid.shape[0] * (grid.shape[1] - 1)
    total_possible_v = (grid.shape[0] - 1) * grid.shape[1]
    
    features.update({
        'horizontal_transitions': horizontal_transitions,
        'vertical_transitions': vertical_transitions,
        'horizontal_transition_ratio': horizontal_transitions / max(total_possible_h, 1),
        'vertical_transition_ratio': vertical_transitions / max(total_possible_v, 1),
        'total_transitions': horizontal_transitions + vertical_transitions,
        'transition_density': (horizontal_transitions + vertical_transitions) / 
                            max(total_possible_h + total_possible_v, 1)
    })
    
    return features

def find_connected_components(grid: np.ndarray, background_color: int = 0) -> Dict[str, Any]:
    """
    Find connected components for each color in the grid.
    
    Args:
        grid: 2D numpy array representing the grid
        background_color: Color to treat as background
        
    Returns:
        Dictionary containing connected component information
    """
    grid = np.array(grid)
    unique_colors = np.unique(grid)
    components_info = {}
    
    for color in unique_colors:
        if color == background_color:
            continue
            
        # Create binary mask for this color
        mask = (grid == color).astype(int)
        
        # Find connected components
        labeled_array, num_features = label(mask)
        
        components = []
        for i in range(1, num_features + 1):
            component_mask = (labeled_array == i)
            coords = np.where(component_mask)
            
            # Bounding box
            min_row, max_row = coords[0].min(), coords[0].max()
            min_col, max_col = coords[1].min(), coords[1].max()
            
            component_info = {
                'size': np.sum(component_mask),
                'bounding_box': (min_row, min_col, max_row, max_col),
                'width': max_col - min_col + 1,
                'height': max_row - min_row + 1,
                'centroid': (coords[0].mean(), coords[1].mean()),
                'coords': list(zip(coords[0], coords[1]))
            }
            
            # Shape analysis
            bbox_area = component_info['width'] * component_info['height']
            component_info['density'] = component_info['size'] / bbox_area
            component_info['aspect_ratio'] = component_info['width'] / component_info['height']
            
            components.append(component_info)
        
        components_info[int(color)] = {
            'num_components': num_features,
            'components': components,
            'total_size': sum(comp['size'] for comp in components)
        }
    
    return components_info

def detect_geometric_shapes(grid: np.ndarray) -> Dict[str, Any]:
    """
    Detect basic geometric shapes in the grid.
    
    Args:
        grid: 2D numpy array representing the grid
        
    Returns:
        Dictionary containing shape detection results
    """
    grid = np.array(grid)
    features = {
        'rectangles': [],
        'lines': [],
        'squares': [],
        'single_pixels': []
    }
    
    # Get connected components
    background_color = Counter(grid.flatten()).most_common(1)[0][0]
    components_info = find_connected_components(grid, background_color)
    
    for color, color_info in components_info.items():
        for component in color_info['components']:
            size = component['size']
            width = component['width']
            height = component['height']
            density = component['density']
            
            # Single pixel
            if size == 1:
                features['single_pixels'].append({
                    'color': color,
                    'position': component['centroid']
                })
            
            # Line detection (horizontal or vertical)
            elif width == 1 or height == 1:
                if density > 0.8:  # Most of bounding box is filled
                    features['lines'].append({
                        'color': color,
                        'orientation': 'horizontal' if height == 1 else 'vertical',
                        'length': max(width, height),
                        'position': component['centroid']
                    })
            
            # Rectangle/Square detection
            elif density > 0.8:  # Filled rectangle
                if abs(width - height) <= 1:  # Square
                    features['squares'].append({
                        'color': color,
                        'size': min(width, height),
                        'position': component['centroid']
                    })
                else:  # Rectangle
                    features['rectangles'].append({
                        'color': color,
                        'width': width,
                        'height': height,
                        'position': component['centroid']
                    })
    
    # Summary statistics
    features['num_rectangles'] = len(features['rectangles'])
    features['num_lines'] = len(features['lines'])
    features['num_squares'] = len(features['squares'])
    features['num_single_pixels'] = len(features['single_pixels'])
    
    return features

def analyze_symmetry(grid: np.ndarray) -> Dict[str, Any]:
    """
    Analyze various types of symmetry in the grid.
    
    Args:
        grid: 2D numpy array representing the grid
        
    Returns:
        Dictionary containing symmetry analysis results
    """
    grid = np.array(grid)
    features = {}
    
    # Horizontal symmetry (top-bottom mirror)
    flipped_horizontal = np.flipud(grid)
    features['horizontal_symmetry'] = np.array_equal(grid, flipped_horizontal)
    if not features['horizontal_symmetry']:
        # Calculate similarity score
        features['horizontal_symmetry_score'] = np.mean(grid == flipped_horizontal)
    else:
        features['horizontal_symmetry_score'] = 1.0
    
    # Vertical symmetry (left-right mirror)
    flipped_vertical = np.fliplr(grid)
    features['vertical_symmetry'] = np.array_equal(grid, flipped_vertical)
    if not features['vertical_symmetry']:
        features['vertical_symmetry_score'] = np.mean(grid == flipped_vertical)
    else:
        features['vertical_symmetry_score'] = 1.0
    
    # Rotational symmetry (90, 180, 270 degrees)
    rotated_90 = np.rot90(grid, k=1)
    rotated_180 = np.rot90(grid, k=2)
    rotated_270 = np.rot90(grid, k=3)
    
    features['rotation_90_symmetry'] = np.array_equal(grid, rotated_90)
    features['rotation_180_symmetry'] = np.array_equal(grid, rotated_180)
    features['rotation_270_symmetry'] = np.array_equal(grid, rotated_270)
    
    # Calculate rotation similarity scores
    if grid.shape[0] == rotated_90.shape[0] and grid.shape[1] == rotated_90.shape[1]:
        features['rotation_90_score'] = np.mean(grid == rotated_90)
    else:
        features['rotation_90_score'] = 0.0
    
    features['rotation_180_score'] = np.mean(grid == rotated_180)
    
    if grid.shape[0] == rotated_270.shape[0] and grid.shape[1] == rotated_270.shape[1]:
        features['rotation_270_score'] = np.mean(grid == rotated_270)
    else:
        features['rotation_270_score'] = 0.0
    
    # Diagonal symmetry (only for square grids)
    if grid.shape[0] == grid.shape[1]:
        # Main diagonal (top-left to bottom-right)
        transposed = grid.T
        features['diagonal_main_symmetry'] = np.array_equal(grid, transposed)
        features['diagonal_main_score'] = np.mean(grid == transposed)
        
        # Anti-diagonal (top-right to bottom-left)
        anti_diagonal = np.fliplr(np.flipud(grid)).T
        features['diagonal_anti_symmetry'] = np.array_equal(grid, anti_diagonal)
        features['diagonal_anti_score'] = np.mean(grid == anti_diagonal)
    else:
        features['diagonal_main_symmetry'] = False
        features['diagonal_main_score'] = 0.0
        features['diagonal_anti_symmetry'] = False
        features['diagonal_anti_score'] = 0.0
    
    # Overall symmetry summary
    symmetry_types = [
        features['horizontal_symmetry'],
        features['vertical_symmetry'],
        features['rotation_90_symmetry'],
        features['rotation_180_symmetry'],
        features['rotation_270_symmetry'],
        features['diagonal_main_symmetry'],
        features['diagonal_anti_symmetry']
    ]
    
    features['num_symmetries'] = sum(symmetry_types)
    features['has_any_symmetry'] = any(symmetry_types)
    
    # Average symmetry score
    symmetry_scores = [
        features['horizontal_symmetry_score'],
        features['vertical_symmetry_score'],
        features['rotation_90_score'],
        features['rotation_180_score'],
        features['rotation_270_score'],
        features['diagonal_main_score'],
        features['diagonal_anti_score']
    ]
    
    features['average_symmetry_score'] = np.mean(symmetry_scores)
    features['max_symmetry_score'] = max(symmetry_scores)

    return features

def extract_spatial_features(grid: np.ndarray) -> Dict[str, Any]:
    """
    Extract spatial and dimensional features from the grid.

    Args:
        grid: 2D numpy array representing the grid

    Returns:
        Dictionary containing spatial features
    """
    grid = np.array(grid)
    height, width = grid.shape

    features = {
        # Basic dimensions
        'height': height,
        'width': width,
        'area': height * width,
        'aspect_ratio': width / height,
        'is_square': height == width,
        'perimeter': 2 * (height + width),

        # Size categories
        'size_category': 'small' if height * width <= 25 else
                        'medium' if height * width <= 100 else 'large',

        # Dimension ratios
        'width_height_ratio': width / height,
        'height_width_ratio': height / width,
    }

    # Find bounding boxes for each color
    background_color = Counter(grid.flatten()).most_common(1)[0][0]
    unique_colors = np.unique(grid)

    bounding_boxes = {}
    for color in unique_colors:
        if color == background_color:
            continue

        coords = np.where(grid == color)
        if len(coords[0]) > 0:
            min_row, max_row = coords[0].min(), coords[0].max()
            min_col, max_col = coords[1].min(), coords[1].max()

            bbox_width = max_col - min_col + 1
            bbox_height = max_row - min_row + 1

            bounding_boxes[int(color)] = {
                'min_row': min_row,
                'max_row': max_row,
                'min_col': min_col,
                'max_col': max_col,
                'width': bbox_width,
                'height': bbox_height,
                'area': bbox_width * bbox_height,
                'center': ((min_row + max_row) / 2, (min_col + max_col) / 2)
            }

    features['bounding_boxes'] = bounding_boxes

    # Overall bounding box (all non-background colors)
    if bounding_boxes:
        all_min_rows = [bbox['min_row'] for bbox in bounding_boxes.values()]
        all_max_rows = [bbox['max_row'] for bbox in bounding_boxes.values()]
        all_min_cols = [bbox['min_col'] for bbox in bounding_boxes.values()]
        all_max_cols = [bbox['max_col'] for bbox in bounding_boxes.values()]

        overall_bbox = {
            'min_row': min(all_min_rows),
            'max_row': max(all_max_rows),
            'min_col': min(all_min_cols),
            'max_col': max(all_max_cols)
        }

        overall_bbox['width'] = overall_bbox['max_col'] - overall_bbox['min_col'] + 1
        overall_bbox['height'] = overall_bbox['max_row'] - overall_bbox['min_row'] + 1
        overall_bbox['area'] = overall_bbox['width'] * overall_bbox['height']
        overall_bbox['coverage'] = overall_bbox['area'] / (height * width)

        features['overall_bounding_box'] = overall_bbox
    else:
        features['overall_bounding_box'] = None

    return features

def extract_pattern_features(grid: np.ndarray) -> Dict[str, Any]:
    """
    Extract pattern-based features from the grid.

    Args:
        grid: 2D numpy array representing the grid

    Returns:
        Dictionary containing pattern features
    """
    grid = np.array(grid)
    features = {}

    # Edge analysis
    top_edge = grid[0, :]
    bottom_edge = grid[-1, :]
    left_edge = grid[:, 0]
    right_edge = grid[:, -1]

    features.update({
        'top_edge_uniform': len(np.unique(top_edge)) == 1,
        'bottom_edge_uniform': len(np.unique(bottom_edge)) == 1,
        'left_edge_uniform': len(np.unique(left_edge)) == 1,
        'right_edge_uniform': len(np.unique(right_edge)) == 1,
        'all_edges_uniform': (len(np.unique(top_edge)) == 1 and
                             len(np.unique(bottom_edge)) == 1 and
                             len(np.unique(left_edge)) == 1 and
                             len(np.unique(right_edge)) == 1),
        'top_edge_color': int(top_edge[0]),
        'bottom_edge_color': int(bottom_edge[0]),
        'left_edge_color': int(left_edge[0]),
        'right_edge_color': int(right_edge[0])
    })

    # Corner analysis
    corners = [
        grid[0, 0],    # top-left
        grid[0, -1],   # top-right
        grid[-1, 0],   # bottom-left
        grid[-1, -1]   # bottom-right
    ]

    features.update({
        'corners': [int(c) for c in corners],
        'corners_uniform': len(np.unique(corners)) == 1,
        'corner_color': int(corners[0]) if len(np.unique(corners)) == 1 else None
    })

    # Center analysis (for odd dimensions)
    if grid.shape[0] % 2 == 1 and grid.shape[1] % 2 == 1:
        center_row = grid.shape[0] // 2
        center_col = grid.shape[1] // 2
        features['center_color'] = int(grid[center_row, center_col])
        features['has_center'] = True
    else:
        features['center_color'] = None
        features['has_center'] = False

    return features

def extract_all_features(grid: np.ndarray) -> Dict[str, Any]:
    """
    Extract all features from a single grid.

    Args:
        grid: 2D numpy array representing the grid

    Returns:
        Dictionary containing all extracted features
    """
    grid = np.array(grid)

    # Extract all feature types
    color_features = extract_color_features(grid)
    color_pattern_features = extract_color_pattern_features(grid)
    shape_features = detect_geometric_shapes(grid)
    symmetry_features = analyze_symmetry(grid)
    spatial_features = extract_spatial_features(grid)
    pattern_features = extract_pattern_features(grid)

    # Combine all features
    all_features = {
        'grid_shape': grid.shape,
        'timestamp': time.time()
    }

    # Add features with prefixes to avoid naming conflicts
    for key, value in color_features.items():
        all_features[f'color_{key}'] = value

    for key, value in color_pattern_features.items():
        all_features[f'pattern_{key}'] = value

    for key, value in shape_features.items():
        all_features[f'shape_{key}'] = value

    for key, value in symmetry_features.items():
        all_features[f'symmetry_{key}'] = value

    for key, value in spatial_features.items():
        all_features[f'spatial_{key}'] = value

    for key, value in pattern_features.items():
        all_features[f'edge_{key}'] = value

    return all_features

def build_feature_database(dataset: Dict[str, Any], max_puzzles: Optional[int] = None) -> Dict[str, Any]:
    """
    Build a comprehensive feature database from a dataset.

    Args:
        dataset: Dictionary containing puzzle data
        max_puzzles: Maximum number of puzzles to process (for testing)

    Returns:
        Dictionary containing feature database
    """
    print(f"Building feature database from {len(dataset)} puzzles...")

    feature_db = {
        'puzzles': {},
        'metadata': {
            'total_puzzles': 0,
            'total_examples': 0,
            'build_time': time.time(),
            'feature_names': set()
        }
    }

    puzzle_ids = list(dataset.keys())
    if max_puzzles:
        puzzle_ids = puzzle_ids[:max_puzzles]

    for i, puzzle_id in enumerate(puzzle_ids):
        if i % 10 == 0:
            print(f"Processing puzzle {i+1}/{len(puzzle_ids)}...")

        puzzle_data = dataset[puzzle_id]
        puzzle_features = {
            'puzzle_id': puzzle_id,
            'train_examples': [],
            'test_examples': []
        }

        # Process training examples
        for j, example in enumerate(puzzle_data.get('train', [])):
            input_features = extract_all_features(example['input'])
            output_features = extract_all_features(example['output'])

            example_features = {
                'example_id': j,
                'input_features': input_features,
                'output_features': output_features,
                'input_grid': np.array(example['input']),
                'output_grid': np.array(example['output'])
            }

            puzzle_features['train_examples'].append(example_features)
            feature_db['metadata']['feature_names'].update(input_features.keys())
            feature_db['metadata']['feature_names'].update(output_features.keys())
            feature_db['metadata']['total_examples'] += 1

        # Process test examples
        for j, example in enumerate(puzzle_data.get('test', [])):
            input_features = extract_all_features(example['input'])

            example_features = {
                'example_id': j,
                'input_features': input_features,
                'input_grid': np.array(example['input'])
            }

            puzzle_features['test_examples'].append(example_features)
            feature_db['metadata']['feature_names'].update(input_features.keys())

        feature_db['puzzles'][puzzle_id] = puzzle_features
        feature_db['metadata']['total_puzzles'] += 1

    # Convert feature names set to list for JSON serialization
    feature_db['metadata']['feature_names'] = sorted(list(feature_db['metadata']['feature_names']))
    feature_db['metadata']['build_duration'] = time.time() - feature_db['metadata']['build_time']

    print(f"✓ Feature database built successfully!")
    print(f"  - {feature_db['metadata']['total_puzzles']} puzzles processed")
    print(f"  - {feature_db['metadata']['total_examples']} examples processed")
    print(f"  - {len(feature_db['metadata']['feature_names'])} unique features extracted")
    print(f"  - Build time: {feature_db['metadata']['build_duration']:.2f} seconds")

    return feature_db

# ============================================================================
# TRANSFORMATION CLASSES
# ============================================================================

class BasicTransformations:
    """
    Basic geometric transformations for ARC grids.
    """

    @staticmethod
    def rotate_90(grid: np.ndarray) -> np.ndarray:
        """Rotate grid 90 degrees clockwise."""
        return np.rot90(grid, k=-1)  # k=-1 for clockwise

    @staticmethod
    def rotate_180(grid: np.ndarray) -> np.ndarray:
        """Rotate grid 180 degrees."""
        return np.rot90(grid, k=2)

    @staticmethod
    def rotate_270(grid: np.ndarray) -> np.ndarray:
        """Rotate grid 270 degrees clockwise (90 degrees counter-clockwise)."""
        return np.rot90(grid, k=1)  # k=1 for counter-clockwise

    @staticmethod
    def flip_horizontal(grid: np.ndarray) -> np.ndarray:
        """Flip grid horizontally (left-right mirror)."""
        return np.fliplr(grid)

    @staticmethod
    def flip_vertical(grid: np.ndarray) -> np.ndarray:
        """Flip grid vertically (top-bottom mirror)."""
        return np.flipud(grid)

    @staticmethod
    def transpose(grid: np.ndarray) -> np.ndarray:
        """Transpose grid (swap rows and columns)."""
        return grid.T

    @staticmethod
    def identity(grid: np.ndarray) -> np.ndarray:
        """Identity transformation (no change)."""
        return grid.copy()

    @classmethod
    def get_all_basic_transformations(cls) -> Dict[str, callable]:
        """Get dictionary of all basic transformations."""
        return {
            'identity': cls.identity,
            'rotate_90': cls.rotate_90,
            'rotate_180': cls.rotate_180,
            'rotate_270': cls.rotate_270,
            'flip_horizontal': cls.flip_horizontal,
            'flip_vertical': cls.flip_vertical,
            'transpose': cls.transpose
        }

class ColorTransformations:
    """
    Color-based transformations for ARC grids.
    """

    @staticmethod
    def map_colors(grid: np.ndarray, color_map: Dict[int, int]) -> np.ndarray:
        """Map colors according to a color mapping dictionary."""
        result = grid.copy()
        for old_color, new_color in color_map.items():
            result[grid == old_color] = new_color
        return result

    @staticmethod
    def replace_color(grid: np.ndarray, old_color: int, new_color: int) -> np.ndarray:
        """Replace all instances of old_color with new_color."""
        result = grid.copy()
        result[grid == old_color] = new_color
        return result

    @staticmethod
    def invert_colors(grid: np.ndarray, max_color: int = 9) -> np.ndarray:
        """Invert colors (0->9, 1->8, etc.)."""
        return max_color - grid

    @staticmethod
    def swap_colors(grid: np.ndarray, color1: int, color2: int) -> np.ndarray:
        """Swap two colors in the grid."""
        result = grid.copy()
        mask1 = (grid == color1)
        mask2 = (grid == color2)
        result[mask1] = color2
        result[mask2] = color1
        return result

    @staticmethod
    def normalize_colors(grid: np.ndarray) -> np.ndarray:
        """Normalize colors to start from 0 and be consecutive."""
        unique_colors = sorted(np.unique(grid))
        color_map = {old: new for new, old in enumerate(unique_colors)}
        return ColorTransformations.map_colors(grid, color_map)

    @classmethod
    def get_all_color_transformations(cls) -> Dict[str, callable]:
        """Get dictionary of all color transformations."""
        return {
            'map_colors': cls.map_colors,
            'replace_color': cls.replace_color,
            'invert_colors': cls.invert_colors,
            'swap_colors': cls.swap_colors,
            'normalize_colors': cls.normalize_colors
        }

# ============================================================================
# EVALUATION AND SEARCH CLASSES
# ============================================================================

class SolutionEvaluator:
    """
    Comprehensive evaluation system for ARC solutions.
    """

    @staticmethod
    def exact_match_score(predicted: np.ndarray, target: np.ndarray) -> float:
        """Calculate exact match score (1.0 if identical, 0.0 otherwise)."""
        if predicted.shape != target.shape:
            return 0.0
        return 1.0 if np.array_equal(predicted, target) else 0.0

    @staticmethod
    def pixel_accuracy_score(predicted: np.ndarray, target: np.ndarray) -> float:
        """Calculate pixel-wise accuracy score."""
        if predicted.shape != target.shape:
            return 0.0
        return np.mean(predicted == target)

    @staticmethod
    def shape_similarity_score(predicted: np.ndarray, target: np.ndarray) -> float:
        """Calculate shape similarity score based on dimensions."""
        pred_h, pred_w = predicted.shape
        target_h, target_w = target.shape

        # Dimension similarity
        h_sim = 1.0 - abs(pred_h - target_h) / max(pred_h, target_h)
        w_sim = 1.0 - abs(pred_w - target_w) / max(pred_w, target_w)

        return (h_sim + w_sim) / 2.0

    @staticmethod
    def color_distribution_score(predicted: np.ndarray, target: np.ndarray) -> float:
        """Calculate color distribution similarity score."""
        pred_colors = Counter(predicted.flatten())
        target_colors = Counter(target.flatten())

        all_colors = set(pred_colors.keys()) | set(target_colors.keys())

        if not all_colors:
            return 1.0

        total_diff = 0
        total_count = predicted.size + target.size

        for color in all_colors:
            pred_count = pred_colors.get(color, 0)
            target_count = target_colors.get(color, 0)
            total_diff += abs(pred_count - target_count)

        return 1.0 - (total_diff / total_count)

    @classmethod
    def comprehensive_score(cls, predicted: np.ndarray, target: np.ndarray,
                          weights: Dict[str, float] = None) -> Dict[str, float]:
        """Calculate comprehensive evaluation score."""
        if weights is None:
            weights = {
                'exact_match': 0.5,
                'pixel_accuracy': 0.3,
                'shape_similarity': 0.1,
                'color_distribution': 0.1
            }

        scores = {
            'exact_match': cls.exact_match_score(predicted, target),
            'pixel_accuracy': cls.pixel_accuracy_score(predicted, target),
            'shape_similarity': cls.shape_similarity_score(predicted, target),
            'color_distribution': cls.color_distribution_score(predicted, target)
        }

        # Calculate weighted overall score
        overall_score = sum(scores[key] * weights[key] for key in scores.keys())
        scores['overall'] = overall_score

        return scores

class ARCHeuristics:
    """
    Heuristic rules and patterns for ARC puzzle solving.
    """

    @staticmethod
    def get_common_transformation_sequences() -> List[List[str]]:
        """Get common transformation sequences observed in ARC puzzles."""
        return [
            ['identity'],
            ['rotate_90'],
            ['rotate_180'],
            ['rotate_270'],
            ['flip_horizontal'],
            ['flip_vertical'],
            ['transpose'],
            ['rotate_90', 'flip_horizontal'],
            ['rotate_180', 'flip_vertical'],
            ['flip_horizontal', 'flip_vertical'],
            ['replace_color'],
            ['invert_colors'],
            ['normalize_colors']
        ]

    @staticmethod
    def suggest_transformations_from_features(input_features: Dict[str, Any],
                                            output_features: Dict[str, Any]) -> List[str]:
        """Suggest transformations based on feature analysis."""
        suggestions = []

        # Shape-based suggestions
        if (input_features.get('spatial_width') == output_features.get('spatial_height') and
            input_features.get('spatial_height') == output_features.get('spatial_width')):
            suggestions.extend(['rotate_90', 'rotate_270', 'transpose'])

        # Symmetry-based suggestions
        if (input_features.get('symmetry_horizontal_symmetry') and
            not output_features.get('symmetry_horizontal_symmetry')):
            suggestions.append('flip_horizontal')

        if (input_features.get('symmetry_vertical_symmetry') and
            not output_features.get('symmetry_vertical_symmetry')):
            suggestions.append('flip_vertical')

        # Color-based suggestions
        input_colors = set(input_features.get('color_unique_colors', []))
        output_colors = set(output_features.get('color_unique_colors', []))

        if input_colors != output_colors:
            suggestions.extend(['replace_color', 'invert_colors', 'normalize_colors'])

        return suggestions if suggestions else ['identity']

class TransformationSearcher:
    """
    Search engine for finding transformation sequences.
    """

    def __init__(self, max_depth: int = 3, max_candidates: int = 50):
        self.max_depth = max_depth
        self.max_candidates = max_candidates
        self.evaluator = SolutionEvaluator()
        self.heuristics = ARCHeuristics()

        # Available transformations
        self.available_transforms = {}
        self.available_transforms.update(BasicTransformations.get_all_basic_transformations())
        self.available_transforms.update(ColorTransformations.get_all_color_transformations())

    def search_transformations(self, input_grid: np.ndarray,
                             target_grid: np.ndarray,
                             suggested_transforms: List[str] = None) -> List[Dict[str, Any]]:
        """Search for transformation sequences that convert input to target."""
        candidates = []

        # Get transformation sequences to try
        if suggested_transforms:
            sequences = self._generate_sequences_from_suggestions(suggested_transforms)
        else:
            sequences = self.heuristics.get_common_transformation_sequences()

        # Add single transformations
        basic_transforms = list(BasicTransformations.get_all_basic_transformations().keys())
        sequences.extend([[t] for t in basic_transforms])

        # Limit number of sequences to try
        sequences = sequences[:self.max_candidates]

        for sequence in sequences:
            try:
                # Apply transformation sequence
                result_grid = self._apply_sequence(input_grid, sequence)

                # Evaluate result
                scores = self.evaluator.comprehensive_score(result_grid, target_grid)

                candidate = {
                    'sequence': sequence,
                    'result_grid': result_grid,
                    'scores': scores,
                    'overall_score': scores['overall']
                }

                candidates.append(candidate)

                # Early termination if perfect match found
                if scores['exact_match'] == 1.0:
                    break

            except Exception as e:
                # Skip failed transformations
                continue

        # Sort by overall score
        candidates.sort(key=lambda x: x['overall_score'], reverse=True)
        return candidates

    def _apply_sequence(self, grid: np.ndarray, sequence: List[str]) -> np.ndarray:
        """Apply a sequence of transformations to a grid."""
        result = grid.copy()

        for transform_name in sequence:
            if transform_name in self.available_transforms:
                transform_func = self.available_transforms[transform_name]

                # Handle transformations that need parameters
                if transform_name in ['map_colors', 'replace_color', 'swap_colors']:
                    # For now, skip parameterized transformations in sequence search
                    continue
                else:
                    result = transform_func(result)
            else:
                raise ValueError(f"Unknown transformation: {transform_name}")

        return result

    def _generate_sequences_from_suggestions(self, suggestions: List[str]) -> List[List[str]]:
        """Generate transformation sequences from suggestions."""
        sequences = []

        # Single transformations
        for transform in suggestions:
            sequences.append([transform])

        # Pairs of transformations
        for i, t1 in enumerate(suggestions):
            for j, t2 in enumerate(suggestions):
                if i != j:
                    sequences.append([t1, t2])

        return sequences[:self.max_candidates]

# ============================================================================
# MAIN SOLVER CLASS
# ============================================================================

class HybridARCSolver:
    """
    Hybrid ARC solver combining feature-based retrieval and transformation search.
    """

    def __init__(self, feature_db: Dict[str, Any] = None,
                 max_retrieval_candidates: int = 5,
                 max_transformation_depth: int = 3):
        """
        Initialize the hybrid solver.

        Args:
            feature_db: Feature database for retrieval
            max_retrieval_candidates: Maximum candidates from retrieval
            max_transformation_depth: Maximum transformation sequence depth
        """
        self.feature_db = feature_db
        self.max_retrieval_candidates = max_retrieval_candidates
        self.max_transformation_depth = max_transformation_depth

        # Initialize components
        self.evaluator = SolutionEvaluator()
        self.heuristics = ARCHeuristics()
        self.searcher = TransformationSearcher(
            max_depth=max_transformation_depth,
            max_candidates=50
        )

        # Initialize retrieval system if feature database is available
        self.retrieval_system = None
        if feature_db:
            self.retrieval_system = self._initialize_retrieval_system()

    def solve_puzzle(self, test_input: np.ndarray,
                    training_examples: List[Dict[str, np.ndarray]] = None,
                    use_retrieval: bool = True,
                    use_heuristics: bool = True) -> Dict[str, Any]:
        """
        Solve a single ARC puzzle.

        Args:
            test_input: Test input grid
            training_examples: List of training examples for this puzzle
            use_retrieval: Whether to use retrieval-based reasoning
            use_heuristics: Whether to use rule-based heuristics

        Returns:
            Dictionary containing solution and metadata
        """
        solution = {
            'predicted_output': None,
            'confidence': 0.0,
            'method': 'unknown',
            'transformation_sequence': [],
            'candidates': [],
            'retrieval_results': [],
            'processing_time': 0.0
        }

        start_time = time.time()

        try:
            # Extract features from test input
            test_features = extract_all_features(test_input)

            # Try different solving approaches
            candidates = []

            # 1. Training example-based approach
            if training_examples:
                train_candidates = self._solve_from_training_examples(
                    test_input, training_examples, test_features
                )
                candidates.extend(train_candidates)

            # 2. Retrieval-based approach
            if use_retrieval and self.retrieval_system:
                retrieval_candidates = self._solve_from_retrieval(
                    test_input, test_features
                )
                candidates.extend(retrieval_candidates)
                solution['retrieval_results'] = retrieval_candidates[:3]  # Top 3

            # 3. Heuristic-based approach
            if use_heuristics:
                heuristic_candidates = self._solve_from_heuristics(
                    test_input, test_features
                )
                candidates.extend(heuristic_candidates)

            # Select best candidate
            if candidates:
                # Sort by confidence/score
                candidates.sort(key=lambda x: x.get('confidence', 0.0), reverse=True)
                best_candidate = candidates[0]

                solution.update({
                    'predicted_output': best_candidate.get('predicted_output'),
                    'confidence': best_candidate.get('confidence', 0.0),
                    'method': best_candidate.get('method', 'unknown'),
                    'transformation_sequence': best_candidate.get('transformation_sequence', []),
                    'candidates': candidates[:5]  # Top 5 candidates
                })

        except Exception as e:
            solution['method'] = 'error'
            solution['error'] = str(e)

        solution['processing_time'] = time.time() - start_time
        return solution

    def _solve_from_training_examples(self, test_input: np.ndarray,
                                    training_examples: List[Dict[str, np.ndarray]],
                                    test_features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Solve using training examples from the same puzzle."""
        candidates = []

        for i, example in enumerate(training_examples):
            try:
                input_grid = example['input']
                output_grid = example['output']

                # Search for transformations that work on this training example
                search_results = self.searcher.search_transformations(
                    input_grid, output_grid
                )

                # Apply the best transformation to test input
                if search_results:
                    best_transform = search_results[0]
                    sequence = best_transform['sequence']

                    # Apply to test input
                    predicted_output = self.searcher._apply_sequence(test_input, sequence)

                    candidate = {
                        'predicted_output': predicted_output,
                        'confidence': best_transform['overall_score'],
                        'method': f'training_example_{i}',
                        'transformation_sequence': sequence,
                        'source_example': i
                    }

                    candidates.append(candidate)

            except Exception as e:
                continue

        return candidates

    def _solve_from_retrieval(self, test_input: np.ndarray,
                            test_features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Solve using retrieval from feature database."""
        candidates = []

        if not self.retrieval_system:
            return candidates

        try:
            # Find similar puzzles
            similar_puzzles = self.retrieval_system.find_similar_puzzles(
                test_features, k=self.max_retrieval_candidates
            )

            for similarity_result in similar_puzzles:
                puzzle_id = similarity_result['puzzle_id']
                similarity_score = similarity_result['similarity']

                # Get the puzzle from feature database
                puzzle_data = self.feature_db['puzzles'].get(puzzle_id)
                if not puzzle_data:
                    continue

                # Try transformations from similar puzzle's training examples
                for example in puzzle_data['train_examples']:
                    try:
                        input_grid = example['input_grid']
                        output_grid = example['output_grid']

                        # Search for transformations
                        search_results = self.searcher.search_transformations(
                            input_grid, output_grid
                        )

                        if search_results:
                            best_transform = search_results[0]
                            sequence = best_transform['sequence']

                            # Apply to test input
                            predicted_output = self.searcher._apply_sequence(test_input, sequence)

                            candidate = {
                                'predicted_output': predicted_output,
                                'confidence': similarity_score * best_transform['overall_score'],
                                'method': f'retrieval_{puzzle_id}',
                                'transformation_sequence': sequence,
                                'source_puzzle': puzzle_id,
                                'similarity_score': similarity_score
                            }

                            candidates.append(candidate)

                    except Exception as e:
                        continue

        except Exception as e:
            pass

        return candidates

    def _solve_from_heuristics(self, test_input: np.ndarray,
                             test_features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Solve using heuristic rules."""
        candidates = []

        try:
            # Get common transformation sequences
            sequences = self.heuristics.get_common_transformation_sequences()

            for sequence in sequences[:10]:  # Try top 10 sequences
                try:
                    predicted_output = self.searcher._apply_sequence(test_input, sequence)

                    # Simple confidence based on whether output is different from input
                    confidence = 0.1  # Base confidence for heuristic approach
                    if not np.array_equal(predicted_output, test_input):
                        confidence = 0.3

                    candidate = {
                        'predicted_output': predicted_output,
                        'confidence': confidence,
                        'method': 'heuristic',
                        'transformation_sequence': sequence
                    }

                    candidates.append(candidate)

                except Exception as e:
                    continue

        except Exception as e:
            pass

        return candidates

    def _initialize_retrieval_system(self):
        """Initialize the retrieval system with feature database."""
        # Simple retrieval system - in practice, you'd use more sophisticated methods
        class SimpleRetrievalSystem:
            def __init__(self, feature_db):
                self.feature_db = feature_db

            def find_similar_puzzles(self, query_features, k=5):
                # Simple similarity based on feature overlap
                similarities = []

                for puzzle_id, puzzle_data in self.feature_db['puzzles'].items():
                    for example in puzzle_data['train_examples']:
                        input_features = example['input_features']

                        # Simple similarity: count matching feature values
                        matches = 0
                        total = 0

                        for key in query_features:
                            if key in input_features:
                                total += 1
                                if query_features[key] == input_features[key]:
                                    matches += 1

                        similarity = matches / max(total, 1)
                        similarities.append({
                            'puzzle_id': puzzle_id,
                            'similarity': similarity
                        })

                # Sort by similarity and return top k
                similarities.sort(key=lambda x: x['similarity'], reverse=True)
                return similarities[:k]

        return SimpleRetrievalSystem(self.feature_db)
