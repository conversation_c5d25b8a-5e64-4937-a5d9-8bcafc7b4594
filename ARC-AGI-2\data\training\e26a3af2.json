{"train": [{"input": [[2, 2, 2, 2, 2, 8, 8, 1, 8, 8, 8, 1, 1, 1], [2, 2, 8, 2, 2, 8, 8, 8, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 9, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 9, 8, 6, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 5, 1, 1, 1], [2, 2, 2, 6, 2, 8, 8, 8, 8, 8, 5, 1, 1, 6], [2, 6, 4, 2, 2, 9, 8, 8, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 6, 8, 7, 8, 8, 8, 1, 1, 2], [2, 2, 2, 6, 2, 8, 3, 8, 5, 8, 8, 3, 1, 1], [2, 2, 2, 2, 5, 8, 2, 8, 5, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 1, 3], [2, 8, 2, 2, 2, 8, 8, 8, 8, 3, 8, 9, 1, 1]], "output": [[2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 1, 1, 1], [2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 1, 1, 1]]}, {"input": [[1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 8, 2], [9, 5, 1, 5, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 2, 2], [4, 1, 1, 2, 1, 8, 8, 5, 3, 3, 8, 3, 2, 8, 2, 2, 7], [1, 1, 1, 1, 1, 8, 8, 2, 3, 3, 3, 3, 2, 2, 2, 2, 2], [9, 1, 1, 1, 8, 8, 8, 8, 3, 3, 4, 3, 8, 2, 2, 2, 2], [4, 1, 2, 1, 1, 7, 8, 8, 3, 3, 3, 3, 2, 2, 6, 2, 9], [1, 1, 1, 1, 9, 8, 8, 8, 9, 3, 3, 3, 4, 2, 6, 2, 2], [1, 1, 1, 1, 1, 8, 5, 8, 3, 3, 3, 4, 2, 2, 2, 2, 3], [1, 1, 1, 9, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 2, 2], [6, 1, 1, 8, 1, 5, 8, 8, 4, 3, 3, 3, 6, 4, 2, 2, 7], [1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 6, 2, 2], [1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 7, 3, 2, 2, 2, 2, 2], [1, 2, 1, 4, 1, 8, 8, 8, 3, 3, 3, 3, 2, 9, 2, 1, 2]], "output": [[1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 8, 8, 8, 3, 3, 3, 3, 2, 2, 2, 2, 2]]}, {"input": [[3, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 2, 3, 3, 2, 3, 3, 3, 3, 3], [3, 3, 3, 9, 3, 3, 3, 2, 3, 3, 3, 9, 3, 3], [3, 3, 4, 3, 3, 2, 3, 3, 3, 3, 3, 3, 3, 3], [7, 7, 7, 7, 7, 7, 8, 7, 7, 3, 3, 7, 7, 4], [9, 7, 7, 7, 3, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 2], [7, 7, 7, 7, 7, 5, 7, 7, 7, 7, 7, 7, 5, 8], [7, 7, 7, 7, 7, 7, 3, 7, 7, 7, 7, 2, 7, 7], [7, 7, 7, 4, 6, 7, 7, 7, 7, 7, 9, 7, 7, 7], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 3, 8, 8], [8, 8, 8, 4, 8, 8, 8, 7, 9, 8, 8, 8, 8, 8], [1, 1, 1, 5, 1, 1, 1, 1, 1, 1, 9, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9, 1, 1, 1]], "output": [[3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]]}], "test": [{"input": [[6, 1, 1, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 4, 1, 1, 9, 1, 1, 1, 1, 5, 1, 1, 1, 1, 1], [5, 1, 1, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [7, 2, 2, 2, 2, 6, 2, 9, 2, 2, 4, 2, 4, 2, 2], [2, 2, 9, 2, 1, 2, 2, 2, 3, 2, 2, 8, 2, 7, 2], [2, 5, 2, 2, 5, 6, 6, 2, 2, 2, 3, 2, 5, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 6, 2, 8, 2, 2], [1, 8, 8, 8, 8, 8, 9, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 1, 8, 8, 8, 8, 8, 7, 8, 8, 8, 9], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 5, 8, 8, 8, 1, 8, 8], [4, 4, 4, 4, 4, 4, 7, 3, 4, 4, 4, 4, 4, 2, 4], [4, 4, 7, 4, 4, 4, 4, 4, 4, 4, 8, 4, 4, 4, 4], [3, 3, 1, 9, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [8, 6, 3, 3, 8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]], "output": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]]}]}