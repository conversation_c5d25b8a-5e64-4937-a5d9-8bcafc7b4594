{"train": [{"input": [[8, 8, 8, 8, 8, 8, 8, 7, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 6, 8, 8, 8, 8, 8, 8, 8, 7], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 6, 1, 8, 8, 8, 7], [8, 8, 6, 8, 8, 8, 6, 8, 8, 8], [8, 8, 1, 8, 8, 8, 1, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 6, 8, 8], [8, 8, 8, 8, 8, 8, 8, 1, 8, 8], [8, 8, 7, 8, 8, 8, 7, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 7, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 6, 8, 8, 8, 8, 8, 8, 8, 7], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 1, 6, 8, 8, 8, 7], [8, 8, 1, 8, 8, 8, 1, 8, 8, 8], [8, 8, 6, 8, 8, 8, 6, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 6, 8, 8], [8, 8, 8, 8, 8, 8, 8, 1, 8, 8], [8, 8, 7, 8, 8, 8, 7, 8, 8, 8]]}, {"input": [[8, 7, 8, 8, 8, 8, 8, 7], [8, 8, 8, 8, 8, 8, 8, 8], [8, 6, 8, 8, 8, 8, 8, 1], [8, 1, 8, 1, 8, 8, 8, 6], [8, 8, 8, 6, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8], [7, 8, 8, 8, 8, 1, 6, 8], [8, 8, 8, 7, 8, 8, 8, 8]], "output": [[8, 7, 8, 8, 8, 8, 8, 7], [8, 8, 8, 8, 8, 8, 8, 8], [8, 6, 8, 8, 8, 8, 8, 6], [8, 1, 8, 1, 8, 8, 8, 1], [8, 8, 8, 6, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8], [7, 8, 8, 8, 8, 6, 1, 8], [8, 8, 8, 7, 8, 8, 8, 8]]}, {"input": [[7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 7, 8], [8, 8, 1, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 7], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 1, 8, 8, 8, 7], [8, 8, 8, 8, 8, 8, 8, 8, 1, 6, 8, 8, 8, 7], [8, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 6, 8], [8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 1, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 1, 6, 8, 8, 8, 8, 7], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [6, 8, 8, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 7, 8], [8, 8, 1, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 7], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 1, 6, 8, 8, 8, 7], [8, 8, 8, 8, 8, 8, 8, 8, 1, 6, 8, 8, 8, 7], [8, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 6, 8], [8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 1, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 1, 6, 8, 8, 8, 8, 7], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 8, 8, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}], "test": [{"input": [[8, 8, 8, 8, 8, 8, 7, 7, 8, 8, 7, 8, 8, 7, 8, 8, 8, 8], [7, 8, 6, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 1, 8, 7], [7, 8, 8, 8, 1, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 1, 6, 8, 8, 8, 8, 8, 8, 8, 7], [8, 8, 8, 8, 8, 8, 8, 8, 6, 1, 8, 8, 8, 8, 8, 8, 8, 7], [7, 8, 8, 8, 8, 8, 8, 8, 1, 6, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 1, 8, 8, 8, 8, 8, 8, 8, 7], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 1, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8], [7, 8, 8, 1, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 1, 1, 8, 8, 8, 8, 8, 6, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 6, 6, 8, 8, 8, 8, 8, 1, 8, 8, 8, 8], [7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 6, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 7, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 7, 7, 8, 8, 7, 8, 8, 7, 8, 8, 8, 8], [7, 8, 6, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 6, 8, 7], [7, 8, 8, 8, 6, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 1, 6, 8, 8, 8, 8, 8, 8, 8, 7], [8, 8, 8, 8, 8, 8, 8, 8, 1, 6, 8, 8, 8, 8, 8, 8, 8, 7], [7, 8, 8, 8, 8, 8, 8, 8, 6, 1, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 1, 6, 8, 8, 8, 8, 8, 8, 8, 7], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8], [7, 8, 8, 6, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 6, 6, 8, 8, 8, 8, 8, 6, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 1, 1, 8, 8, 8, 8, 8, 1, 8, 8, 8, 8], [7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 1, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 7, 8, 8, 8, 8, 8, 8]]}]}