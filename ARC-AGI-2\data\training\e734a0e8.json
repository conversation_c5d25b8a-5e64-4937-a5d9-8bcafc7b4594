{"train": [{"input": [[7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 9, 9, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 9, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 9, 7, 0, 7, 7, 0, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 9, 9, 9, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 9, 7, 7, 7, 0, 7, 7, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 0, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 0, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7]], "output": [[7, 7, 7, 7, 7, 0, 7, 7, 7, 9, 9, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 9, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 9, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 9, 9, 9, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 9, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 7, 7, 0, 7, 7, 7, 9, 9, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 9, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 9, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 9, 9, 9, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 9, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 9, 9, 0, 7, 7, 7, 9, 9], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 9, 7, 0, 7, 7, 7, 9, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 9, 7, 0, 7, 7, 7, 9, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 9, 9, 9, 7, 0, 7, 9, 9, 9, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 9, 7, 7, 7, 0, 7, 9, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 9, 9, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 9, 9], [7, 7, 7, 9, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 9, 7], [7, 7, 7, 9, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 9, 7], [7, 9, 9, 9, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 9, 9, 9, 7], [7, 9, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 9, 7, 7, 7]]}, {"input": [[7, 2, 7, 0, 7, 7, 7, 0, 7, 7, 7], [7, 2, 7, 0, 7, 7, 7, 0, 7, 7, 7], [7, 2, 7, 0, 7, 7, 7, 0, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 0, 7, 7, 7, 0, 7, 7, 7], [7, 7, 7, 0, 7, 0, 7, 0, 7, 7, 7], [7, 7, 7, 0, 7, 7, 7, 0, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 0, 7, 7, 7, 0, 7, 7, 7], [7, 7, 7, 0, 7, 0, 7, 0, 7, 0, 7], [7, 7, 7, 0, 7, 7, 7, 0, 7, 7, 7]], "output": [[7, 2, 7, 0, 7, 7, 7, 0, 7, 7, 7], [7, 2, 7, 0, 7, 7, 7, 0, 7, 7, 7], [7, 2, 7, 0, 7, 7, 7, 0, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 0, 7, 2, 7, 0, 7, 7, 7], [7, 7, 7, 0, 7, 2, 7, 0, 7, 7, 7], [7, 7, 7, 0, 7, 2, 7, 0, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 0, 7, 2, 7, 0, 7, 2, 7], [7, 7, 7, 0, 7, 2, 7, 0, 7, 2, 7], [7, 7, 7, 0, 7, 2, 7, 0, 7, 2, 7]]}], "test": [{"input": [[7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 4, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 4, 4, 4, 4, 4], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 4, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 4, 7, 4, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 4, 7, 7, 7, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 0, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 0, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7]], "output": [[7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 4, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 4, 4, 4, 4, 4], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 4, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 4, 7, 4, 7], [7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 4, 7, 7, 7, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 7, 7, 0, 7, 7, 4, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 4, 4, 4, 4, 4, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7, 4, 7, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 4, 7, 4, 7, 0, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 4, 7, 7, 7, 4, 0, 7, 7, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 4, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 4, 7, 7], [4, 4, 4, 4, 4, 0, 7, 7, 7, 7, 7, 0, 4, 4, 4, 4, 4], [7, 7, 4, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 4, 7, 7], [7, 4, 7, 4, 7, 0, 7, 7, 7, 7, 7, 0, 7, 4, 7, 4, 7], [4, 7, 7, 7, 4, 0, 7, 7, 7, 7, 7, 0, 4, 7, 7, 7, 4]]}]}