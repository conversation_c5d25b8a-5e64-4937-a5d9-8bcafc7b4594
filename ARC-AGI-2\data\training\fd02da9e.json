{"train": [{"input": [[7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [1, 7, 7, 7, 7, 7, 7, 7]], "output": [[7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 1, 7, 7, 7, 7, 7], [7, 7, 1, 7, 7, 7, 7, 7], [7, 7, 7, 1, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7]]}, {"input": [[7, 7, 7, 7, 7, 7, 7, 8], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7]], "output": [[7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 8, 8, 7], [7, 7, 7, 7, 7, 8, 8, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7]]}, {"input": [[7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 0]], "output": [[7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7], [7, 7, 7, 7, 7, 0, 7, 7], [7, 7, 7, 7, 0, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7]]}, {"input": [[9, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7]], "output": [[7, 7, 7, 7, 7, 7, 7, 7], [7, 9, 9, 7, 7, 7, 7, 7], [7, 9, 9, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7]]}], "test": [{"input": [[2, 7, 7, 7, 7, 7, 7, 5], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7], [8, 7, 7, 7, 7, 7, 7, 4]], "output": [[7, 7, 7, 7, 7, 7, 7, 7], [7, 2, 2, 7, 7, 5, 5, 7], [7, 2, 2, 7, 7, 5, 5, 7], [7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 8, 7, 7, 4, 7, 7], [7, 7, 8, 7, 7, 4, 7, 7], [7, 7, 7, 8, 4, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7]]}]}