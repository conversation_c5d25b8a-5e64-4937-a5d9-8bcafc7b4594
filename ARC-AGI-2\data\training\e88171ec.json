{"train": [{"input": [[0, 0, 2, 2, 2, 0, 2, 2, 0, 0, 0, 0, 2, 2, 2], [2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 0, 2, 0, 2, 2], [2, 2, 0, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 2, 0], [2, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0, 2, 2, 0, 2], [2, 0, 2, 2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 2, 2], [0, 2, 0, 2, 2, 0, 0, 0, 0, 0, 2, 2, 0, 2, 0], [2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 0, 2, 2, 2], [0, 0, 2, 2, 0, 0, 0, 2, 2, 2, 0, 2, 0, 2, 2], [2, 2, 2, 2, 0, 2, 2, 2, 0, 0, 2, 0, 0, 2, 2], [0, 0, 0, 2, 2, 2, 2, 0, 2, 0, 2, 2, 2, 2, 2], [2, 2, 0, 2, 2, 2, 2, 0, 0, 2, 2, 0, 0, 2, 0], [2, 2, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 2, 2, 0], [2, 0, 2, 2, 2, 0, 0, 2, 0, 0, 2, 2, 2, 2, 2], [0, 2, 2, 2, 2, 0, 0, 0, 2, 2, 2, 2, 2, 2, 0], [0, 2, 0, 2, 0, 2, 2, 2, 2, 2, 0, 2, 2, 2, 0]], "output": [[0, 0, 2, 2, 2, 0, 2, 2, 0, 0, 0, 0, 2, 2, 2], [2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 0, 2, 0, 2, 2], [2, 2, 0, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 2, 0], [2, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0, 2, 2, 0, 2], [2, 0, 2, 2, 0, 0, 8, 8, 0, 2, 0, 0, 0, 2, 2], [0, 2, 0, 2, 2, 0, 8, 8, 0, 0, 2, 2, 0, 2, 0], [2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 0, 2, 2, 2], [0, 0, 2, 2, 0, 0, 0, 2, 2, 2, 0, 2, 0, 2, 2], [2, 2, 2, 2, 0, 2, 2, 2, 0, 0, 2, 0, 0, 2, 2], [0, 0, 0, 2, 2, 2, 2, 0, 2, 0, 2, 2, 2, 2, 2], [2, 2, 0, 2, 2, 2, 2, 0, 0, 2, 2, 0, 0, 2, 0], [2, 2, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 2, 2, 0], [2, 0, 2, 2, 2, 0, 0, 2, 0, 0, 2, 2, 2, 2, 2], [0, 2, 2, 2, 2, 0, 0, 0, 2, 2, 2, 2, 2, 2, 0], [0, 2, 0, 2, 0, 2, 2, 2, 2, 2, 0, 2, 2, 2, 0]]}, {"input": [[4, 0, 0, 4, 0, 0, 0, 4, 0, 0, 5, 0, 0, 0, 0, 4, 4, 4, 4], [0, 4, 4, 4, 4, 5, 4, 4, 0, 0, 0, 4, 4, 4, 0, 4, 0, 4, 0], [0, 0, 4, 4, 4, 0, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 0, 4], [0, 4, 0, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 0, 5, 0, 5, 4, 4], [4, 0, 4, 4, 0, 0, 0, 0, 0, 4, 4, 0, 4, 0, 4, 0, 4, 0, 4], [4, 4, 4, 0, 0, 0, 0, 0, 0, 4, 0, 0, 4, 0, 0, 4, 4, 0, 4], [4, 4, 0, 0, 0, 0, 0, 0, 0, 4, 4, 4, 0, 0, 4, 4, 4, 4, 4], [4, 4, 4, 4, 0, 0, 0, 0, 0, 4, 4, 4, 4, 5, 4, 4, 0, 5, 4], [4, 4, 4, 0, 0, 0, 0, 0, 0, 4, 5, 4, 4, 4, 0, 4, 0, 0, 5], [0, 4, 4, 4, 0, 0, 0, 0, 0, 4, 4, 0, 4, 4, 5, 4, 0, 0, 4], [4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 0, 4, 0, 4, 0, 4, 4, 5], [4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 5, 5, 4, 0, 4, 0, 4, 4, 5], [4, 4, 4, 4, 4, 5, 0, 4, 0, 4, 0, 4, 4, 0, 4, 0, 5, 4, 4], [5, 4, 4, 0, 4, 4, 0, 0, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4], [4, 0, 4, 0, 4, 0, 4, 4, 4, 4, 4, 4, 0, 4, 0, 4, 0, 4, 4], [5, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 0, 0, 4, 4, 4, 0], [0, 0, 4, 4, 0, 4, 4, 4, 0, 0, 4, 0, 4, 0, 0, 0, 0, 4, 4], [4, 0, 0, 4, 4, 5, 4, 5, 4, 5, 4, 0, 4, 4, 0, 4, 4, 5, 0], [4, 0, 0, 4, 4, 0, 0, 0, 5, 4, 4, 0, 0, 4, 4, 5, 4, 4, 0]], "output": [[4, 0, 0, 4, 0, 0, 0, 4, 0, 0, 5, 0, 0, 0, 0, 4, 4, 4, 4], [0, 4, 4, 4, 4, 5, 4, 4, 0, 0, 0, 4, 4, 4, 0, 4, 0, 4, 0], [0, 0, 4, 4, 4, 0, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 0, 4], [0, 4, 0, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 0, 5, 0, 5, 4, 4], [4, 0, 4, 4, 0, 0, 0, 0, 0, 4, 4, 0, 4, 0, 4, 0, 4, 0, 4], [4, 4, 4, 0, 0, 8, 8, 8, 0, 4, 0, 0, 4, 0, 0, 4, 4, 0, 4], [4, 4, 0, 0, 0, 8, 8, 8, 0, 4, 4, 4, 0, 0, 4, 4, 4, 4, 4], [4, 4, 4, 4, 0, 8, 8, 8, 0, 4, 4, 4, 4, 5, 4, 4, 0, 5, 4], [4, 4, 4, 0, 0, 8, 8, 8, 0, 4, 5, 4, 4, 4, 0, 4, 0, 0, 5], [0, 4, 4, 4, 0, 0, 0, 0, 0, 4, 4, 0, 4, 4, 5, 4, 0, 0, 4], [4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 0, 4, 0, 4, 0, 4, 4, 5], [4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 5, 5, 4, 0, 4, 0, 4, 4, 5], [4, 4, 4, 4, 4, 5, 0, 4, 0, 4, 0, 4, 4, 0, 4, 0, 5, 4, 4], [5, 4, 4, 0, 4, 4, 0, 0, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4], [4, 0, 4, 0, 4, 0, 4, 4, 4, 4, 4, 4, 0, 4, 0, 4, 0, 4, 4], [5, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 0, 0, 4, 4, 4, 0], [0, 0, 4, 4, 0, 4, 4, 4, 0, 0, 4, 0, 4, 0, 0, 0, 0, 4, 4], [4, 0, 0, 4, 4, 5, 4, 5, 4, 5, 4, 0, 4, 4, 0, 4, 4, 5, 0], [4, 0, 0, 4, 4, 0, 0, 0, 5, 4, 4, 0, 0, 4, 4, 5, 4, 4, 0]]}, {"input": [[0, 0, 3, 0, 3, 3, 3, 0, 0, 0, 0, 0, 3, 3, 3, 0], [0, 0, 3, 0, 0, 3, 0, 3, 0, 0, 0, 3, 3, 0, 3, 3], [0, 3, 0, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 0, 0], [3, 3, 3, 3, 3, 0, 3, 0, 3, 3, 3, 3, 0, 3, 3, 3], [3, 3, 0, 3, 0, 0, 3, 0, 0, 3, 3, 3, 0, 0, 3, 3], [0, 0, 3, 3, 0, 0, 3, 3, 3, 3, 3, 0, 0, 3, 3, 0], [3, 0, 3, 3, 3, 0, 0, 0, 0, 3, 0, 3, 3, 3, 3, 3], [0, 0, 0, 0, 3, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3, 0], [3, 3, 3, 0, 3, 3, 0, 3, 0, 3, 0, 3, 3, 3, 3, 0], [3, 0, 0, 3, 0, 0, 0, 0, 3, 3, 3, 3, 0, 3, 3, 3], [0, 0, 0, 3, 0, 3, 3, 0, 0, 0, 0, 0, 0, 3, 0, 0], [0, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3], [3, 0, 0, 0, 3, 0, 3, 0, 0, 0, 0, 0, 0, 3, 3, 3], [0, 0, 0, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3], [3, 3, 3, 3, 3, 3, 0, 0, 0, 3, 3, 3, 3, 0, 3, 0], [0, 0, 0, 3, 3, 0, 0, 3, 3, 0, 3, 3, 0, 0, 3, 3]], "output": [[0, 0, 3, 0, 3, 3, 3, 0, 0, 0, 0, 0, 3, 3, 3, 0], [0, 0, 3, 0, 0, 3, 0, 3, 0, 0, 0, 3, 3, 0, 3, 3], [0, 3, 0, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 0, 0], [3, 3, 3, 3, 3, 0, 3, 0, 3, 3, 3, 3, 0, 3, 3, 3], [3, 3, 0, 3, 0, 0, 3, 0, 0, 3, 3, 3, 0, 0, 3, 3], [0, 0, 3, 3, 0, 0, 3, 3, 3, 3, 3, 0, 0, 3, 3, 0], [3, 0, 3, 3, 3, 0, 0, 0, 0, 3, 0, 3, 3, 3, 3, 3], [0, 0, 0, 0, 3, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3, 0], [3, 3, 3, 0, 3, 3, 0, 3, 0, 3, 0, 3, 3, 3, 3, 0], [3, 0, 0, 3, 0, 0, 0, 0, 3, 3, 3, 3, 0, 3, 3, 3], [0, 0, 0, 3, 0, 3, 3, 0, 0, 0, 0, 0, 0, 3, 0, 0], [0, 3, 3, 3, 3, 0, 0, 0, 8, 8, 8, 8, 0, 0, 3, 3], [3, 0, 0, 0, 3, 0, 3, 0, 8, 8, 8, 8, 0, 3, 3, 3], [0, 0, 0, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3], [3, 3, 3, 3, 3, 3, 0, 0, 0, 3, 3, 3, 3, 0, 3, 0], [0, 0, 0, 3, 3, 0, 0, 3, 3, 0, 3, 3, 0, 0, 3, 3]]}], "test": [{"input": [[7, 7, 0, 0, 0, 7, 7, 7, 0, 0, 0, 7, 0, 0, 7, 7, 0, 7, 0, 7, 7], [7, 0, 7, 7, 7, 0, 0, 0, 0, 7, 7, 7, 7, 7, 7, 0, 0, 7, 7, 0, 0], [7, 7, 7, 7, 7, 0, 7, 0, 0, 7, 7, 7, 7, 7, 0, 0, 7, 7, 7, 7, 0], [7, 0, 0, 7, 0, 7, 7, 7, 0, 0, 7, 0, 0, 0, 0, 7, 0, 0, 7, 7, 0], [7, 7, 7, 7, 0, 7, 7, 0, 7, 0, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 7], [7, 7, 0, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 7, 7], [0, 7, 7, 7, 0, 0, 7, 7, 7, 7, 0, 0, 7, 0, 0, 7, 7, 7, 7, 7, 7], [7, 0, 0, 7, 0, 0, 7, 7, 7, 7, 0, 7, 0, 7, 7, 7, 7, 0, 7, 7, 7], [7, 7, 7, 0, 7, 0, 7, 7, 7, 0, 7, 7, 7, 7, 7, 7, 7, 0, 0, 0, 7], [7, 7, 7, 0, 7, 7, 7, 7, 0, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7], [0, 7, 7, 0, 7, 0, 7, 0, 0, 7, 7, 7, 7, 7, 0, 7, 0, 0, 0, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 0, 7, 0, 7, 7, 7, 7, 7, 7, 0, 0, 0], [7, 7, 0, 0, 0, 7, 7, 0, 7, 7, 0, 0, 0, 0, 0, 0, 0, 7, 0, 7, 7], [0, 7, 7, 0, 0, 7, 0, 0, 7, 7, 0, 0, 0, 0, 0, 0, 7, 7, 0, 0, 7], [7, 7, 7, 0, 7, 7, 0, 7, 7, 7, 0, 0, 0, 0, 0, 0, 0, 7, 0, 7, 0], [7, 0, 7, 7, 0, 7, 0, 7, 0, 7, 0, 0, 0, 0, 0, 0, 7, 7, 7, 0, 0], [7, 7, 7, 7, 7, 7, 0, 0, 7, 7, 0, 0, 0, 0, 0, 0, 7, 7, 0, 7, 7], [0, 0, 7, 7, 0, 7, 0, 0, 7, 7, 0, 0, 0, 7, 7, 0, 0, 7, 0, 0, 7], [7, 0, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 7, 0, 7, 0, 0, 0, 0, 0, 0], [0, 7, 7, 0, 0, 7, 7, 0, 7, 0, 0, 0, 0, 7, 0, 7, 7, 7, 7, 7, 7], [0, 7, 7, 0, 7, 7, 7, 0, 0, 7, 7, 0, 0, 7, 7, 0, 7, 7, 0, 7, 7]], "output": [[7, 7, 0, 0, 0, 7, 7, 7, 0, 0, 0, 7, 0, 0, 7, 7, 0, 7, 0, 7, 7], [7, 0, 7, 7, 7, 0, 0, 0, 0, 7, 7, 7, 7, 7, 7, 0, 0, 7, 7, 0, 0], [7, 7, 7, 7, 7, 0, 7, 0, 0, 7, 7, 7, 7, 7, 0, 0, 7, 7, 7, 7, 0], [7, 0, 0, 7, 0, 7, 7, 7, 0, 0, 7, 0, 0, 0, 0, 7, 0, 0, 7, 7, 0], [7, 7, 7, 7, 0, 7, 7, 0, 7, 0, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 7], [7, 7, 0, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 7, 7], [0, 7, 7, 7, 0, 0, 7, 7, 7, 7, 0, 0, 7, 0, 0, 7, 7, 7, 7, 7, 7], [7, 0, 0, 7, 0, 0, 7, 7, 7, 7, 0, 7, 0, 7, 7, 7, 7, 0, 7, 7, 7], [7, 7, 7, 0, 7, 0, 7, 7, 7, 0, 7, 7, 7, 7, 7, 7, 7, 0, 0, 0, 7], [7, 7, 7, 0, 7, 7, 7, 7, 0, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7], [0, 7, 7, 0, 7, 0, 7, 0, 0, 7, 7, 7, 7, 7, 0, 7, 0, 0, 0, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 0, 7, 0, 7, 7, 7, 7, 7, 7, 0, 0, 0], [7, 7, 0, 0, 0, 7, 7, 0, 7, 7, 0, 0, 0, 0, 0, 0, 0, 7, 0, 7, 7], [0, 7, 7, 0, 0, 7, 0, 0, 7, 7, 0, 8, 8, 8, 8, 0, 7, 7, 0, 0, 7], [7, 7, 7, 0, 7, 7, 0, 7, 7, 7, 0, 8, 8, 8, 8, 0, 0, 7, 0, 7, 0], [7, 0, 7, 7, 0, 7, 0, 7, 0, 7, 0, 8, 8, 8, 8, 0, 7, 7, 7, 0, 0], [7, 7, 7, 7, 7, 7, 0, 0, 7, 7, 0, 0, 0, 0, 0, 0, 7, 7, 0, 7, 7], [0, 0, 7, 7, 0, 7, 0, 0, 7, 7, 0, 0, 0, 7, 7, 0, 0, 7, 0, 0, 7], [7, 0, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 7, 0, 7, 0, 0, 0, 0, 0, 0], [0, 7, 7, 0, 0, 7, 7, 0, 7, 0, 0, 0, 0, 7, 0, 7, 7, 7, 7, 7, 7], [0, 7, 7, 0, 7, 7, 7, 0, 0, 7, 7, 0, 0, 7, 7, 0, 7, 7, 0, 7, 7]]}]}