#!/usr/bin/env python3
"""
Advanced Retrieval System for ARC-AGI-2

This module implements sophisticated case-based reasoning for ARC puzzle solving,
including advanced similarity metrics, feature weighting, and retrieval strategies.

Key Features:
- Multiple similarity metrics (cosine, Euclidean, custom ARC metrics)
- Feature importance weighting
- Hierarchical retrieval (puzzle-level and example-level)
- Transformation pattern matching
- Confidence scoring for retrieved solutions
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from collections import Counter, defaultdict
from sklearn.metrics.pairwise import cosine_similarity, euclidean_distances
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import time

from arc_solver_components import extract_all_features

class AdvancedFeatureVectorizer:
    """
    Advanced feature vectorization with importance weighting and normalization.
    """
    
    def __init__(self):
        self.feature_weights = self._get_feature_importance_weights()
        self.scaler = StandardScaler()
        self.pca = None
        self.feature_names = None
        self.is_fitted = False
    
    def _get_feature_importance_weights(self) -> Dict[str, float]:
        """Get importance weights for different feature categories."""
        return {
            # Shape and spatial features (high importance)
            'spatial_': 2.0,
            'grid_shape': 3.0,
            'shape_': 1.5,
            
            # Color features (medium-high importance)
            'color_num_colors': 2.5,
            'color_unique_colors': 2.0,
            'color_background_color': 1.5,
            'color_': 1.0,
            
            # Symmetry features (medium importance)
            'symmetry_': 1.2,
            
            # Pattern features (medium importance)
            'pattern_': 1.0,
            'edge_': 0.8,
            
            # Default weight
            'default': 0.5
        }
    
    def vectorize_features(self, features: Dict[str, Any]) -> np.ndarray:
        """
        Convert feature dictionary to weighted numerical vector.
        
        Args:
            features: Dictionary of extracted features
            
        Returns:
            Numpy array representing the weighted feature vector
        """
        if self.feature_names is None:
            self.feature_names = self._get_numerical_feature_names(features)
        
        vector = []
        weights = []
        
        for feature_name in self.feature_names:
            value = features.get(feature_name, 0)
            
            # Convert to numerical value
            if isinstance(value, (int, float)):
                numerical_value = float(value)
            elif isinstance(value, bool):
                numerical_value = 1.0 if value else 0.0
            elif isinstance(value, (list, tuple)):
                # For lists, use length or sum
                if all(isinstance(x, (int, float)) for x in value):
                    numerical_value = float(sum(value))
                else:
                    numerical_value = float(len(value))
            elif isinstance(value, dict):
                # For dictionaries, use number of keys or sum of values
                if all(isinstance(v, (int, float)) for v in value.values()):
                    numerical_value = float(sum(value.values()))
                else:
                    numerical_value = float(len(value))
            else:
                numerical_value = 0.0
            
            # Get weight for this feature
            weight = self._get_feature_weight(feature_name)
            
            vector.append(numerical_value)
            weights.append(weight)
        
        # Apply weights
        vector = np.array(vector)
        weights = np.array(weights)
        weighted_vector = vector * weights
        
        return weighted_vector
    
    def _get_numerical_feature_names(self, features: Dict[str, Any]) -> List[str]:
        """Get list of feature names that can be converted to numerical values."""
        numerical_features = []
        
        for key, value in features.items():
            if isinstance(value, (int, float, bool)):
                numerical_features.append(key)
            elif isinstance(value, (list, tuple, dict)):
                # Include collections that can be converted to numbers
                numerical_features.append(key)
            # Skip complex objects like numpy arrays
        
        return sorted(numerical_features)
    
    def _get_feature_weight(self, feature_name: str) -> float:
        """Get importance weight for a feature."""
        for prefix, weight in self.feature_weights.items():
            if feature_name.startswith(prefix):
                return weight
        return self.feature_weights['default']
    
    def fit_transform(self, feature_list: List[Dict[str, Any]]) -> np.ndarray:
        """
        Fit the vectorizer and transform a list of feature dictionaries.
        
        Args:
            feature_list: List of feature dictionaries
            
        Returns:
            2D numpy array of feature vectors
        """
        # Vectorize all features
        vectors = []
        for features in feature_list:
            vector = self.vectorize_features(features)
            vectors.append(vector)
        
        vectors = np.array(vectors)
        
        # Fit and transform with scaler
        vectors = self.scaler.fit_transform(vectors)
        
        # Optional PCA for dimensionality reduction
        if vectors.shape[1] > 50:  # Only if we have many features
            self.pca = PCA(n_components=min(50, vectors.shape[0] - 1))
            vectors = self.pca.fit_transform(vectors)
        
        self.is_fitted = True
        return vectors
    
    def transform(self, features: Dict[str, Any]) -> np.ndarray:
        """
        Transform a single feature dictionary to vector.
        
        Args:
            features: Feature dictionary
            
        Returns:
            Feature vector
        """
        if not self.is_fitted:
            raise ValueError("Vectorizer must be fitted first")
        
        vector = self.vectorize_features(features).reshape(1, -1)
        vector = self.scaler.transform(vector)
        
        if self.pca is not None:
            vector = self.pca.transform(vector)
        
        return vector.flatten()

class AdvancedSimilarityMetrics:
    """
    Advanced similarity metrics for ARC puzzle comparison.
    """
    
    @staticmethod
    def cosine_similarity_score(vec1: np.ndarray, vec2: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors."""
        if len(vec1.shape) == 1:
            vec1 = vec1.reshape(1, -1)
        if len(vec2.shape) == 1:
            vec2 = vec2.reshape(1, -1)
        
        similarity = cosine_similarity(vec1, vec2)[0, 0]
        return max(0.0, similarity)  # Ensure non-negative
    
    @staticmethod
    def euclidean_similarity_score(vec1: np.ndarray, vec2: np.ndarray) -> float:
        """Calculate Euclidean similarity (inverse of distance)."""
        if len(vec1.shape) == 1:
            vec1 = vec1.reshape(1, -1)
        if len(vec2.shape) == 1:
            vec2 = vec2.reshape(1, -1)
        
        distance = euclidean_distances(vec1, vec2)[0, 0]
        # Convert distance to similarity (0-1 range)
        similarity = 1.0 / (1.0 + distance)
        return similarity
    
    @staticmethod
    def arc_specific_similarity(features1: Dict[str, Any], 
                              features2: Dict[str, Any]) -> float:
        """
        Calculate ARC-specific similarity based on key puzzle characteristics.
        
        Args:
            features1: First feature dictionary
            features2: Second feature dictionary
            
        Returns:
            Similarity score between 0 and 1
        """
        similarity_scores = []
        
        # Grid shape similarity
        shape1 = features1.get('grid_shape', (0, 0))
        shape2 = features2.get('grid_shape', (0, 0))
        
        if shape1 == shape2:
            shape_sim = 1.0
        else:
            # Partial similarity based on aspect ratio
            ratio1 = shape1[1] / max(shape1[0], 1)
            ratio2 = shape2[1] / max(shape2[0], 1)
            shape_sim = 1.0 - abs(ratio1 - ratio2) / max(ratio1, ratio2, 1)
        
        similarity_scores.append(('shape', shape_sim, 3.0))
        
        # Color similarity
        colors1 = set(features1.get('color_unique_colors', []))
        colors2 = set(features2.get('color_unique_colors', []))
        
        if colors1 and colors2:
            color_intersection = len(colors1 & colors2)
            color_union = len(colors1 | colors2)
            color_sim = color_intersection / color_union if color_union > 0 else 0.0
        else:
            color_sim = 1.0 if not colors1 and not colors2 else 0.0
        
        similarity_scores.append(('color', color_sim, 2.0))
        
        # Symmetry similarity
        sym_features = ['symmetry_horizontal_symmetry', 'symmetry_vertical_symmetry', 
                       'symmetry_rotation_180_symmetry']
        
        sym_matches = 0
        sym_total = 0
        
        for sym_feature in sym_features:
            val1 = features1.get(sym_feature, False)
            val2 = features2.get(sym_feature, False)
            
            if val1 == val2:
                sym_matches += 1
            sym_total += 1
        
        sym_sim = sym_matches / sym_total if sym_total > 0 else 1.0
        similarity_scores.append(('symmetry', sym_sim, 1.5))
        
        # Shape count similarity
        shapes1 = {
            'rectangles': features1.get('shape_num_rectangles', 0),
            'lines': features1.get('shape_num_lines', 0),
            'squares': features1.get('shape_num_squares', 0)
        }
        
        shapes2 = {
            'rectangles': features2.get('shape_num_rectangles', 0),
            'lines': features2.get('shape_num_lines', 0),
            'squares': features2.get('shape_num_squares', 0)
        }
        
        shape_diffs = []
        for shape_type in shapes1:
            diff = abs(shapes1[shape_type] - shapes2[shape_type])
            max_count = max(shapes1[shape_type], shapes2[shape_type], 1)
            shape_diffs.append(1.0 - diff / max_count)
        
        shape_count_sim = np.mean(shape_diffs)
        similarity_scores.append(('shape_count', shape_count_sim, 1.0))
        
        # Calculate weighted average
        total_weight = sum(weight for _, _, weight in similarity_scores)
        weighted_sum = sum(score * weight for _, score, weight in similarity_scores)
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0
    
    @staticmethod
    def combined_similarity(vec1: np.ndarray, vec2: np.ndarray,
                          features1: Dict[str, Any], features2: Dict[str, Any],
                          weights: Dict[str, float] = None) -> float:
        """
        Calculate combined similarity using multiple metrics.
        
        Args:
            vec1: First feature vector
            vec2: Second feature vector
            features1: First feature dictionary
            features2: Second feature dictionary
            weights: Weights for different similarity metrics
            
        Returns:
            Combined similarity score
        """
        if weights is None:
            weights = {
                'cosine': 0.3,
                'euclidean': 0.2,
                'arc_specific': 0.5
            }
        
        cosine_sim = AdvancedSimilarityMetrics.cosine_similarity_score(vec1, vec2)
        euclidean_sim = AdvancedSimilarityMetrics.euclidean_similarity_score(vec1, vec2)
        arc_sim = AdvancedSimilarityMetrics.arc_specific_similarity(features1, features2)
        
        combined_sim = (
            weights['cosine'] * cosine_sim +
            weights['euclidean'] * euclidean_sim +
            weights['arc_specific'] * arc_sim
        )
        
        return combined_sim

class AdvancedRetrievalSystem:
    """
    Advanced retrieval system for case-based reasoning in ARC puzzles.
    """
    
    def __init__(self, feature_db: Dict[str, Any]):
        self.feature_db = feature_db
        self.vectorizer = AdvancedFeatureVectorizer()
        self.similarity_metrics = AdvancedSimilarityMetrics()
        
        # Build retrieval index
        self._build_retrieval_index()
    
    def _build_retrieval_index(self):
        """Build retrieval index from feature database."""
        print("Building advanced retrieval index...")
        
        self.puzzle_index = []
        self.example_features = []
        self.example_vectors = []
        
        # Collect all examples and their features
        for puzzle_id, puzzle_data in self.feature_db['puzzles'].items():
            for example in puzzle_data['train_examples']:
                input_features = example['input_features']
                output_features = example['output_features']
                
                # Store example metadata
                self.puzzle_index.append({
                    'puzzle_id': puzzle_id,
                    'example_id': example['example_id'],
                    'input_features': input_features,
                    'output_features': output_features,
                    'input_grid': example['input_grid'],
                    'output_grid': example['output_grid']
                })
                
                self.example_features.append(input_features)
        
        # Fit vectorizer and create feature vectors
        if self.example_features:
            self.example_vectors = self.vectorizer.fit_transform(self.example_features)
            print(f"✓ Built retrieval index with {len(self.puzzle_index)} examples")
            print(f"  Feature vector dimension: {self.example_vectors.shape[1]}")
        else:
            print("✗ No examples found in feature database")
    
    def find_similar_examples(self, query_features: Dict[str, Any], 
                            k: int = 10,
                            similarity_threshold: float = 0.1) -> List[Dict[str, Any]]:
        """
        Find similar examples using advanced similarity metrics.
        
        Args:
            query_features: Features of the query puzzle
            k: Number of similar examples to retrieve
            similarity_threshold: Minimum similarity threshold
            
        Returns:
            List of similar examples with similarity scores
        """
        if not self.example_features:
            return []
        
        # Vectorize query features
        query_vector = self.vectorizer.transform(query_features)
        
        # Calculate similarities
        similarities = []
        
        for i, (example_features, example_vector) in enumerate(
            zip(self.example_features, self.example_vectors)
        ):
            # Calculate combined similarity
            similarity = self.similarity_metrics.combined_similarity(
                query_vector, example_vector, query_features, example_features
            )
            
            if similarity >= similarity_threshold:
                example_info = self.puzzle_index[i].copy()
                example_info['similarity'] = similarity
                similarities.append(example_info)
        
        # Sort by similarity and return top k
        similarities.sort(key=lambda x: x['similarity'], reverse=True)
        return similarities[:k]
    
    def find_similar_puzzles(self, query_features: Dict[str, Any], 
                           k: int = 5) -> List[Dict[str, Any]]:
        """
        Find similar puzzles (aggregated from examples).
        
        Args:
            query_features: Features of the query puzzle
            k: Number of similar puzzles to retrieve
            
        Returns:
            List of similar puzzles with aggregated similarity scores
        """
        # Get similar examples
        similar_examples = self.find_similar_examples(query_features, k * 3)
        
        # Aggregate by puzzle
        puzzle_similarities = defaultdict(list)
        
        for example in similar_examples:
            puzzle_id = example['puzzle_id']
            similarity = example['similarity']
            puzzle_similarities[puzzle_id].append(similarity)
        
        # Calculate aggregated similarity for each puzzle
        puzzle_scores = []
        for puzzle_id, similarities in puzzle_similarities.items():
            # Use max similarity as the puzzle similarity
            max_similarity = max(similarities)
            avg_similarity = np.mean(similarities)
            
            # Weighted combination
            aggregated_similarity = 0.7 * max_similarity + 0.3 * avg_similarity
            
            puzzle_scores.append({
                'puzzle_id': puzzle_id,
                'similarity': aggregated_similarity,
                'num_similar_examples': len(similarities),
                'example_similarities': similarities
            })
        
        # Sort and return top k
        puzzle_scores.sort(key=lambda x: x['similarity'], reverse=True)
        return puzzle_scores[:k]
    
    def get_transformation_patterns(self, similar_examples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract transformation patterns from similar examples.
        
        Args:
            similar_examples: List of similar examples
            
        Returns:
            List of transformation patterns with confidence scores
        """
        patterns = []
        
        for example in similar_examples:
            input_grid = example['input_grid']
            output_grid = example['output_grid']
            similarity = example['similarity']
            
            # Analyze the transformation
            pattern = self._analyze_transformation(input_grid, output_grid)
            pattern['confidence'] = similarity
            pattern['source_puzzle'] = example['puzzle_id']
            pattern['source_example'] = example['example_id']
            
            patterns.append(pattern)
        
        return patterns
    
    def _analyze_transformation(self, input_grid: np.ndarray, 
                              output_grid: np.ndarray) -> Dict[str, Any]:
        """
        Analyze the transformation between input and output grids.
        
        Args:
            input_grid: Input grid
            output_grid: Output grid
            
        Returns:
            Dictionary describing the transformation pattern
        """
        pattern = {
            'input_shape': input_grid.shape,
            'output_shape': output_grid.shape,
            'shape_change': input_grid.shape != output_grid.shape,
            'size_ratio': (output_grid.size / input_grid.size) if input_grid.size > 0 else 1.0
        }
        
        # Color analysis
        input_colors = set(input_grid.flatten())
        output_colors = set(output_grid.flatten())
        
        pattern.update({
            'input_colors': input_colors,
            'output_colors': output_colors,
            'color_change': input_colors != output_colors,
            'new_colors': output_colors - input_colors,
            'removed_colors': input_colors - output_colors
        })
        
        # Geometric analysis
        if input_grid.shape == output_grid.shape:
            # Check for simple geometric transformations
            transformations = {
                'identity': np.array_equal(input_grid, output_grid),
                'horizontal_flip': np.array_equal(np.fliplr(input_grid), output_grid),
                'vertical_flip': np.array_equal(np.flipud(input_grid), output_grid),
                'rotate_90': np.array_equal(np.rot90(input_grid, k=-1), output_grid),
                'rotate_180': np.array_equal(np.rot90(input_grid, k=2), output_grid),
                'rotate_270': np.array_equal(np.rot90(input_grid, k=1), output_grid),
            }
            
            pattern['geometric_transformations'] = {
                k: v for k, v in transformations.items() if v
            }
        
        return pattern
