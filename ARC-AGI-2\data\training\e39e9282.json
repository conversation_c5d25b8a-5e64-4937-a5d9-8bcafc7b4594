{"train": [{"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 6, 6, 6, 8, 8, 8, 8, 8, 8, 8], [8, 6, 6, 6, 8, 8, 5, 5, 5, 8, 8], [9, 6, 6, 6, 8, 8, 5, 5, 5, 9, 8], [8, 8, 8, 8, 8, 8, 5, 5, 5, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 9, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 6, 6, 6, 8, 8, 8, 8, 8], [8, 8, 8, 6, 6, 6, 8, 8, 8, 8, 8], [8, 8, 8, 6, 6, 6, 8, 8, 8, 8, 8], [8, 8, 8, 8, 9, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 6, 6, 6, 8, 8, 8, 8, 8, 8, 8], [8, 6, 6, 6, 8, 8, 8, 8, 8, 8, 8], [8, 6, 9, 6, 8, 8, 8, 8, 9, 9, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 6, 6, 6, 8, 8, 8, 8, 8], [8, 8, 8, 9, 9, 6, 8, 8, 8, 8, 8], [8, 8, 8, 6, 6, 6, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}, {"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 6, 6, 6, 8, 8, 8, 8], [8, 8, 6, 6, 6, 8, 8, 8, 8], [8, 8, 6, 6, 6, 9, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 6, 6, 6, 8, 8, 8, 8], [8, 8, 6, 6, 6, 8, 8, 8, 8], [8, 8, 6, 9, 6, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8]]}, {"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 5, 5, 5, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 5, 5, 5, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8], [8, 8, 5, 5, 5, 9, 8, 8, 8, 8, 6, 6, 6, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 8, 8], [8, 8, 8, 8, 6, 6, 6, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 9, 6, 6, 6, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 6, 6, 6, 8, 8, 9, 8, 8, 8, 8, 8], [8, 8, 8, 8, 9, 8, 8, 8, 5, 5, 5, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 5, 5, 5, 8, 8, 8, 8], [8, 8, 8, 8, 5, 5, 5, 8, 5, 5, 5, 8, 8, 8, 8], [8, 8, 8, 8, 5, 5, 5, 8, 8, 8, 9, 8, 8, 8, 8], [8, 8, 8, 9, 5, 5, 5, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 6, 6, 6, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 9, 6, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 8, 8], [8, 8, 8, 8, 6, 6, 6, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 9, 9, 6, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 6, 6, 6, 8, 8, 9, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 8, 8], [8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}], "test": [{"input": [[8, 8, 8, 8, 8, 5, 5, 5, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 5, 5, 5, 8, 8, 8, 8, 8], [6, 6, 6, 8, 9, 5, 5, 5, 9, 8, 8, 8, 8], [6, 6, 6, 8, 8, 8, 9, 8, 8, 8, 8, 8, 8], [6, 6, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [9, 9, 9, 8, 6, 6, 6, 9, 8, 9, 8, 9, 8], [8, 8, 8, 8, 6, 6, 6, 8, 8, 6, 6, 6, 9], [8, 8, 8, 9, 6, 6, 6, 8, 9, 6, 6, 6, 8], [8, 8, 8, 8, 8, 9, 8, 8, 8, 6, 6, 6, 9], [8, 5, 5, 5, 8, 8, 8, 9, 8, 8, 8, 8, 8], [9, 5, 5, 5, 8, 6, 6, 6, 8, 8, 5, 5, 5], [9, 5, 5, 5, 8, 6, 6, 6, 8, 9, 5, 5, 5], [8, 8, 8, 9, 8, 6, 6, 6, 8, 8, 5, 5, 5]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [6, 6, 6, 8, 9, 9, 9, 9, 9, 8, 8, 8, 8], [9, 9, 9, 8, 8, 8, 9, 8, 8, 8, 8, 8, 8], [6, 6, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 6, 9, 6, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 6, 9, 6, 8, 8, 6, 9, 6, 8], [8, 8, 8, 8, 6, 9, 6, 8, 8, 9, 9, 9, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 9, 6, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [9, 9, 8, 8, 8, 6, 6, 6, 8, 8, 8, 8, 8], [9, 9, 8, 9, 8, 6, 6, 9, 8, 9, 9, 8, 8], [8, 8, 8, 9, 8, 6, 6, 6, 8, 8, 8, 8, 8]]}]}