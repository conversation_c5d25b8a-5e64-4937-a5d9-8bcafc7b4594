{"train": [{"input": [[0, 0, 0, 0, 1, 0, 0, 1, 0, 0], [0, 0, 0, 0, 1, 0, 0, 1, 0, 0], [0, 0, 0, 0, 1, 0, 0, 1, 1, 1], [0, 0, 0, 0, 1, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 1, 1, 1, 1, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[5, 1, 5, 5, 1, 5, 5, 1, 5, 5], [5, 1, 5, 5, 1, 5, 5, 1, 5, 5], [5, 1, 5, 5, 1, 5, 5, 1, 1, 1], [5, 1, 5, 5, 1, 5, 5, 5, 5, 5], [5, 1, 5, 5, 1, 5, 5, 5, 5, 5], [5, 1, 5, 5, 1, 1, 1, 1, 1, 1], [5, 1, 5, 5, 5, 5, 5, 5, 5, 5], [5, 1, 5, 5, 5, 5, 5, 5, 5, 5], [5, 1, 1, 1, 1, 1, 1, 1, 1, 1], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [8, 8, 8, 8, 0, 8, 0, 0, 0, 0], [0, 0, 0, 8, 0, 8, 0, 0, 0, 0], [8, 8, 0, 8, 0, 8, 0, 0, 0, 0], [0, 8, 0, 8, 0, 8, 0, 0, 0, 0]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [5, 5, 5, 5, 5, 5, 5, 5, 5, 8], [8, 8, 8, 8, 8, 8, 8, 8, 5, 8], [5, 5, 5, 5, 5, 5, 5, 8, 5, 8], [8, 8, 8, 8, 8, 8, 5, 8, 5, 8], [5, 5, 5, 5, 5, 8, 5, 8, 5, 8], [8, 8, 8, 8, 5, 8, 5, 8, 5, 8], [5, 5, 5, 8, 5, 8, 5, 8, 5, 8], [8, 8, 5, 8, 5, 8, 5, 8, 5, 8], [5, 8, 5, 8, 5, 8, 5, 8, 5, 8]]}, {"input": [[0, 2, 0, 2, 0, 2, 0, 2, 0, 0], [0, 2, 0, 2, 2, 2, 0, 2, 0, 0], [0, 2, 0, 0, 0, 0, 0, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[5, 2, 5, 2, 5, 2, 5, 2, 5, 2], [5, 2, 5, 2, 2, 2, 5, 2, 5, 2], [5, 2, 5, 5, 5, 5, 5, 2, 5, 2], [5, 2, 2, 2, 2, 2, 2, 2, 5, 2], [5, 5, 5, 5, 5, 5, 5, 5, 5, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2]]}], "test": [{"input": [[4, 4, 4, 4, 4, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0], [4, 4, 4, 0, 0, 4, 0, 0, 0, 0], [0, 0, 4, 0, 0, 4, 0, 0, 0, 0], [0, 0, 4, 0, 0, 4, 0, 0, 0, 0], [4, 4, 4, 0, 0, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 0, 0, 0, 0]], "output": [[4, 4, 4, 4, 4, 4, 5, 5, 4, 5], [5, 5, 5, 5, 5, 4, 5, 5, 4, 5], [5, 5, 5, 5, 5, 4, 5, 5, 4, 5], [4, 4, 4, 5, 5, 4, 5, 5, 4, 5], [5, 5, 4, 5, 5, 4, 5, 5, 4, 5], [5, 5, 4, 5, 5, 4, 5, 5, 4, 5], [4, 4, 4, 5, 5, 4, 5, 5, 4, 5], [5, 5, 5, 5, 5, 4, 5, 5, 4, 5], [5, 5, 5, 5, 5, 4, 5, 5, 4, 5], [4, 4, 4, 4, 4, 4, 5, 5, 4, 5]]}]}