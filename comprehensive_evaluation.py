#!/usr/bin/env python3
"""
Comprehensive ARC-AGI-2 Solver Evaluation

This script provides a complete evaluation of all solver improvements:
- Enhanced transformations
- Advanced retrieval system
- Runtime optimization
- Kaggle compliance testing
"""

import sys
import time
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any
import json
import pickle

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from arc_large_scale_evaluation import ARCDatasetLoader, LargeScaleEvaluator
from arc_solver_components import HybridARCSolver, build_feature_database
from enhanced_solver import EnhancedARCSolver

class ComprehensiveEvaluator:
    """
    Comprehensive evaluation system for ARC solver performance.
    """
    
    def __init__(self, data_dir: str = "ARC-AGI-2/data"):
        self.data_dir = data_dir
        self.loader = ARCDatasetLoader(data_dir)
        self.results = {}
        
    def run_comprehensive_evaluation(self, 
                                   max_puzzles: int = 50,
                                   max_feature_puzzles: int = 25,
                                   max_runtime_per_puzzle: float = 20.0,
                                   save_results: bool = True) -> Dict[str, Any]:
        """
        Run comprehensive evaluation comparing all solver versions.
        
        Args:
            max_puzzles: Maximum puzzles to evaluate
            max_feature_puzzles: Maximum puzzles for feature database
            max_runtime_per_puzzle: Maximum runtime per puzzle
            save_results: Whether to save detailed results
            
        Returns:
            Comprehensive evaluation results
        """
        print("Starting Comprehensive ARC-AGI-2 Solver Evaluation")
        print("=" * 60)
        
        # Load and split data
        training_data = self.loader.load_training_dataset(
            max_puzzles=max_puzzles + max_feature_puzzles,
            shuffle=True,
            seed=42
        )
        
        if len(training_data) < max_feature_puzzles + 10:
            print(f"✗ Not enough training data. Need at least {max_feature_puzzles + 10}, got {len(training_data)}")
            return {}
        
        # Split data
        all_puzzle_ids = list(training_data.keys())
        feature_puzzle_ids = all_puzzle_ids[:max_feature_puzzles]
        eval_puzzle_ids = all_puzzle_ids[max_feature_puzzles:max_feature_puzzles + max_puzzles]
        
        feature_data = {pid: training_data[pid] for pid in feature_puzzle_ids}
        eval_data = {pid: training_data[pid] for pid in eval_puzzle_ids}
        
        print(f"Using {len(feature_data)} puzzles for feature database")
        print(f"Evaluating on {len(eval_data)} puzzles")
        
        # Build feature database
        print("\nBuilding feature database...")
        feature_db = build_feature_database(feature_data, max_puzzles=max_feature_puzzles)
        
        # Create solvers
        solvers = {
            'basic': HybridARCSolver(
                feature_db=None,  # No retrieval
                max_transformation_depth=2
            ),
            'with_retrieval': HybridARCSolver(
                feature_db=feature_db,
                max_retrieval_candidates=3,
                max_transformation_depth=2
            ),
            'enhanced': EnhancedARCSolver(
                feature_db=feature_db,
                max_retrieval_candidates=5,
                max_transformation_depth=2
            )
        }
        
        # Evaluate each solver
        evaluation_results = {}
        
        for solver_name, solver in solvers.items():
            print(f"\n{'='*60}")
            print(f"EVALUATING {solver_name.upper()} SOLVER")
            print("="*60)
            
            evaluator = LargeScaleEvaluator(solver, max_runtime_per_puzzle)
            
            # Special handling for enhanced solver
            if solver_name == 'enhanced':
                evaluator = self._create_enhanced_evaluator(solver, max_runtime_per_puzzle)
            
            start_time = time.time()
            stats = evaluator.evaluate_dataset(
                eval_data,
                max_puzzles=max_puzzles,
                save_results=save_results,
                results_file=f"{solver_name}_results.pkl"
            )
            
            evaluation_time = time.time() - start_time
            
            evaluation_results[solver_name] = {
                'stats': stats,
                'evaluation_time': evaluation_time,
                'detailed_results': evaluator.results
            }
        
        # Generate comprehensive comparison
        comparison_results = self._generate_comparison_report(evaluation_results)
        
        # Save comprehensive results
        if save_results:
            self._save_comprehensive_results(evaluation_results, comparison_results)
        
        return {
            'evaluation_results': evaluation_results,
            'comparison_results': comparison_results,
            'dataset_info': {
                'feature_puzzles': len(feature_data),
                'eval_puzzles': len(eval_data),
                'total_puzzles': len(training_data)
            }
        }
    
    def _create_enhanced_evaluator(self, solver, max_runtime_per_puzzle):
        """Create evaluator for enhanced solver."""
        class EnhancedEvaluator(LargeScaleEvaluator):
            def evaluate_puzzle(self, puzzle_id: str, puzzle_data: dict):
                start_time = time.time()
                
                try:
                    train_examples = puzzle_data.get('train', [])
                    test_examples = puzzle_data.get('test', [])
                    
                    if not test_examples:
                        return self._create_failed_result(puzzle_id, "No test examples", 0.0)
                    
                    training_examples = []
                    for example in train_examples:
                        training_examples.append({
                            'input': np.array(example['input']),
                            'output': np.array(example['output'])
                        })
                    
                    test_input = np.array(test_examples[0]['input'])
                    
                    if time.time() - start_time > self.max_runtime_per_puzzle:
                        return self._create_failed_result(puzzle_id, "Timeout", time.time() - start_time)
                    
                    # Use enhanced solving method
                    solution = self.solver.solve_puzzle_enhanced(
                        test_input=test_input,
                        training_examples=training_examples,
                        use_retrieval=True,
                        use_heuristics=True,
                        use_smart_search=True
                    )
                    
                    runtime = time.time() - start_time
                    
                    # Calculate scores
                    solved = False
                    partial_score = 0.0
                    
                    expected_output = None
                    if 'output' in test_examples[0]:
                        expected_output = np.array(test_examples[0]['output'])
                    
                    if solution['predicted_output'] is not None and expected_output is not None:
                        predicted = solution['predicted_output']
                        if np.array_equal(predicted, expected_output):
                            solved = True
                            partial_score = 1.0
                        else:
                            if predicted.shape == expected_output.shape:
                                partial_score = np.mean(predicted == expected_output)
                            else:
                                partial_score = 0.1
                    
                    from arc_large_scale_evaluation import PuzzleResult
                    return PuzzleResult(
                        puzzle_id=puzzle_id,
                        solved=solved,
                        partial_score=partial_score,
                        runtime=runtime,
                        method=solution.get('method', 'unknown'),
                        confidence=solution.get('confidence', 0.0),
                        transformation_sequence=solution.get('transformation_sequence', [])
                    )
                    
                except Exception as e:
                    runtime = time.time() - start_time
                    return self._create_failed_result(puzzle_id, str(e), runtime)
            
            def _create_failed_result(self, puzzle_id, error_msg, runtime):
                from arc_large_scale_evaluation import PuzzleResult
                return PuzzleResult(
                    puzzle_id=puzzle_id,
                    solved=False,
                    partial_score=0.0,
                    runtime=runtime,
                    method="error",
                    confidence=0.0,
                    transformation_sequence=[],
                    error_message=error_msg
                )
        
        return EnhancedEvaluator(solver, max_runtime_per_puzzle)
    
    def _generate_comparison_report(self, evaluation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive comparison report."""
        comparison = {
            'solver_comparison': {},
            'performance_metrics': {},
            'kaggle_compliance': {},
            'recommendations': []
        }
        
        # Extract key metrics
        for solver_name, results in evaluation_results.items():
            stats = results['stats']
            
            comparison['solver_comparison'][solver_name] = {
                'solve_rate': stats.solve_rate,
                'avg_partial_score': stats.avg_partial_score,
                'avg_runtime': stats.avg_runtime,
                'total_runtime': stats.total_runtime,
                'method_breakdown': stats.method_breakdown
            }
        
        # Performance improvements
        if 'basic' in evaluation_results and 'enhanced' in evaluation_results:
            basic_stats = evaluation_results['basic']['stats']
            enhanced_stats = evaluation_results['enhanced']['stats']
            
            comparison['performance_metrics'] = {
                'solve_rate_improvement': enhanced_stats.solve_rate - basic_stats.solve_rate,
                'partial_score_improvement': enhanced_stats.avg_partial_score - basic_stats.avg_partial_score,
                'runtime_change': enhanced_stats.avg_runtime - basic_stats.avg_runtime,
                'relative_solve_improvement': (enhanced_stats.solve_rate / max(basic_stats.solve_rate, 0.001)) - 1
            }
        
        # Kaggle compliance check
        for solver_name, results in evaluation_results.items():
            stats = results['stats']
            
            # Estimate time for 400 puzzles
            estimated_400_runtime = stats.avg_runtime * 400
            kaggle_compliant = estimated_400_runtime < 12 * 3600  # 12 hours
            
            comparison['kaggle_compliance'][solver_name] = {
                'avg_runtime_per_puzzle': stats.avg_runtime,
                'estimated_400_puzzle_runtime_hours': estimated_400_runtime / 3600,
                'kaggle_compliant': kaggle_compliant,
                'runtime_efficiency': 'good' if stats.avg_runtime < 10 else 'acceptable' if stats.avg_runtime < 30 else 'poor'
            }
        
        # Generate recommendations
        best_solver = max(evaluation_results.keys(), 
                         key=lambda x: evaluation_results[x]['stats'].solve_rate)
        
        comparison['recommendations'] = [
            f"Best performing solver: {best_solver}",
            f"Best solve rate: {evaluation_results[best_solver]['stats'].solve_rate:.1%}",
            f"Best partial score: {evaluation_results[best_solver]['stats'].avg_partial_score:.3f}"
        ]
        
        # Add specific recommendations based on performance
        best_stats = evaluation_results[best_solver]['stats']
        
        if best_stats.solve_rate < 0.10:
            comparison['recommendations'].append("Priority: Improve basic transformation coverage")
        elif best_stats.solve_rate < 0.20:
            comparison['recommendations'].append("Priority: Fine-tune search parameters and add domain rules")
        else:
            comparison['recommendations'].append("Priority: Optimize runtime for large-scale deployment")
        
        if best_stats.avg_runtime > 20:
            comparison['recommendations'].append("Consider implementing early stopping and caching")
        
        return comparison
    
    def _save_comprehensive_results(self, evaluation_results: Dict[str, Any], 
                                  comparison_results: Dict[str, Any]):
        """Save comprehensive results to files."""
        timestamp = int(time.time())
        
        # Save detailed results
        with open(f'comprehensive_evaluation_{timestamp}.pkl', 'wb') as f:
            pickle.dump({
                'evaluation_results': evaluation_results,
                'comparison_results': comparison_results
            }, f)
        
        # Save summary report
        with open(f'evaluation_summary_{timestamp}.txt', 'w', encoding='utf-8') as f:
            f.write("ARC-AGI-2 COMPREHENSIVE SOLVER EVALUATION\n")
            f.write("=" * 50 + "\n\n")
            
            # Solver comparison
            f.write("SOLVER PERFORMANCE COMPARISON:\n")
            f.write("-" * 30 + "\n")
            
            for solver_name, metrics in comparison_results['solver_comparison'].items():
                f.write(f"\n{solver_name.upper()} SOLVER:\n")
                f.write(f"  Solve Rate: {metrics['solve_rate']:.1%}\n")
                f.write(f"  Avg Partial Score: {metrics['avg_partial_score']:.3f}\n")
                f.write(f"  Avg Runtime: {metrics['avg_runtime']:.2f}s\n")
                f.write(f"  Total Runtime: {metrics['total_runtime']/60:.1f}min\n")
            
            # Performance improvements
            if 'performance_metrics' in comparison_results:
                f.write(f"\nPERFORMANCE IMPROVEMENTS:\n")
                f.write("-" * 25 + "\n")
                metrics = comparison_results['performance_metrics']
                f.write(f"Solve Rate Improvement: {metrics['solve_rate_improvement']:+.1%}\n")
                f.write(f"Partial Score Improvement: {metrics['partial_score_improvement']:+.3f}\n")
                f.write(f"Runtime Change: {metrics['runtime_change']:+.2f}s\n")
                f.write(f"Relative Improvement: {metrics['relative_solve_improvement']:+.1%}\n")
            
            # Kaggle compliance
            f.write(f"\nKAGGLE COMPLIANCE:\n")
            f.write("-" * 18 + "\n")
            
            for solver_name, compliance in comparison_results['kaggle_compliance'].items():
                f.write(f"\n{solver_name.upper()}:\n")
                f.write(f"  Estimated 400-puzzle runtime: {compliance['estimated_400_puzzle_runtime_hours']:.1f}h\n")
                f.write(f"  Kaggle compliant: {'YES' if compliance['kaggle_compliant'] else 'NO'}\n")
                f.write(f"  Runtime efficiency: {compliance['runtime_efficiency']}\n")
            
            # Recommendations
            f.write(f"\nRECOMMENDATIONS:\n")
            f.write("-" * 15 + "\n")
            for rec in comparison_results['recommendations']:
                f.write(f"• {rec}\n")
        
        print(f"\n✓ Comprehensive results saved:")
        print(f"  - Detailed data: comprehensive_evaluation_{timestamp}.pkl")
        print(f"  - Summary report: evaluation_summary_{timestamp}.txt")

def main():
    """Run comprehensive evaluation."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Run comprehensive ARC solver evaluation')
    parser.add_argument('--max_puzzles', type=int, default=30,
                       help='Maximum puzzles to evaluate (default: 30)')
    parser.add_argument('--max_feature_puzzles', type=int, default=15,
                       help='Maximum puzzles for feature database (default: 15)')
    parser.add_argument('--max_runtime', type=float, default=20.0,
                       help='Maximum runtime per puzzle in seconds (default: 20.0)')
    parser.add_argument('--save_results', action='store_true',
                       help='Save detailed results to files')
    
    args = parser.parse_args()
    
    evaluator = ComprehensiveEvaluator()
    
    results = evaluator.run_comprehensive_evaluation(
        max_puzzles=args.max_puzzles,
        max_feature_puzzles=args.max_feature_puzzles,
        max_runtime_per_puzzle=args.max_runtime,
        save_results=args.save_results
    )
    
    if results:
        print("\n🎉 Comprehensive evaluation completed successfully!")
        
        # Print key findings
        comparison = results['comparison_results']
        
        print(f"\nKEY FINDINGS:")
        print("-" * 20)
        
        best_solver = None
        best_solve_rate = 0
        
        for solver_name, metrics in comparison['solver_comparison'].items():
            solve_rate = metrics['solve_rate']
            if solve_rate > best_solve_rate:
                best_solve_rate = solve_rate
                best_solver = solver_name
        
        print(f"Best solver: {best_solver} ({best_solve_rate:.1%} solve rate)")
        
        if 'performance_metrics' in comparison:
            improvement = comparison['performance_metrics']['solve_rate_improvement']
            print(f"Enhanced solver improvement: {improvement:+.1%}")
        
        # Kaggle compliance
        kaggle_compliant_solvers = [
            name for name, compliance in comparison['kaggle_compliance'].items()
            if compliance['kaggle_compliant']
        ]
        
        if kaggle_compliant_solvers:
            print(f"Kaggle compliant solvers: {', '.join(kaggle_compliant_solvers)}")
        else:
            print("⚠ No solvers are currently Kaggle compliant")
        
        print(f"\nRecommendations:")
        for rec in comparison['recommendations'][:3]:
            print(f"• {rec}")
    
    else:
        print("✗ Comprehensive evaluation failed")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
