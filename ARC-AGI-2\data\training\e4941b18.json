{"train": [{"input": [[7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7], [2, 7, 7, 7, 8, 7, 7, 7, 7], [5, 5, 5, 5, 5, 5, 7, 7, 7], [5, 5, 5, 5, 5, 5, 7, 7, 7], [5, 5, 5, 5, 5, 5, 7, 7, 7], [5, 5, 5, 5, 5, 5, 7, 7, 7]], "output": [[7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 2, 7, 7, 7, 7], [5, 5, 5, 5, 5, 5, 7, 7, 7], [5, 5, 5, 5, 5, 5, 7, 7, 7], [5, 5, 5, 5, 5, 5, 7, 7, 7], [5, 5, 5, 5, 5, 5, 8, 7, 7]]}, {"input": [[7, 7, 7, 7, 7, 7, 7], [7, 7, 2, 7, 8, 7, 7], [7, 7, 5, 5, 5, 7, 7], [7, 7, 5, 5, 5, 7, 7], [7, 7, 5, 5, 5, 7, 7], [7, 7, 5, 5, 5, 7, 7], [7, 7, 5, 5, 5, 7, 7]], "output": [[7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 2, 7, 7], [7, 7, 5, 5, 5, 7, 7], [7, 7, 5, 5, 5, 7, 7], [7, 7, 5, 5, 5, 7, 7], [7, 7, 5, 5, 5, 7, 7], [7, 7, 5, 5, 5, 8, 7]]}, {"input": [[7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 8, 7, 7, 2, 7, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7]], "output": [[7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 2, 7, 7, 7, 7, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 7, 5, 5, 5, 5, 5, 5, 7, 7, 7], [7, 8, 5, 5, 5, 5, 5, 5, 7, 7, 7]]}], "test": [{"input": [[7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 2, 7, 7, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7]], "output": [[7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 2, 7, 7, 7, 7, 7, 7, 7, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7], [8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 7]]}]}