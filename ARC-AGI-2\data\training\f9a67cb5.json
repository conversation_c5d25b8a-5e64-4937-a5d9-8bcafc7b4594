{"train": [{"input": [[0, 0, 8, 0, 0, 8, 0, 0, 0, 8, 0, 0], [0, 0, 8, 0, 0, 8, 0, 0, 0, 8, 0, 0], [0, 0, 8, 0, 0, 8, 0, 0, 0, 8, 0, 0], [2, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0], [0, 0, 8, 0, 0, 8, 0, 0, 0, 8, 0, 0], [0, 0, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0], [0, 0, 8, 0, 0, 8, 0, 0, 0, 0, 0, 0], [0, 0, 8, 0, 0, 8, 0, 0, 0, 8, 0, 0], [0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0], [0, 0, 8, 0, 0, 8, 0, 0, 0, 8, 0, 0], [0, 0, 8, 0, 0, 8, 0, 0, 0, 8, 0, 0]], "output": [[0, 2, 8, 0, 0, 8, 0, 0, 2, 8, 0, 0], [0, 2, 8, 0, 0, 8, 0, 0, 2, 8, 0, 0], [0, 2, 8, 0, 0, 8, 0, 0, 2, 8, 0, 0], [2, 2, 8, 0, 2, 2, 2, 2, 2, 8, 0, 0], [0, 2, 8, 0, 2, 8, 0, 0, 2, 8, 0, 0], [0, 2, 2, 2, 2, 8, 0, 0, 2, 8, 0, 0], [0, 0, 8, 0, 2, 8, 0, 0, 2, 2, 2, 2], [0, 0, 8, 0, 2, 8, 0, 0, 0, 8, 0, 0], [0, 0, 0, 0, 2, 8, 0, 0, 0, 0, 0, 0], [0, 0, 8, 0, 2, 8, 0, 0, 0, 8, 0, 0], [0, 0, 8, 0, 2, 8, 0, 0, 0, 8, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 0, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 2, 8, 8, 8, 2, 8, 8, 8, 8, 8, 8, 8], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0], [8, 8, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 2, 8, 8, 8, 8], [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [8, 8, 8, 8, 8, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 8, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 8, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 8, 0, 0, 8, 0, 0, 0, 8, 0, 0, 2], [0, 0, 8, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 8, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0], [0, 0, 8, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0]], "output": [[0, 0, 8, 0, 0, 8, 2, 0, 0, 8, 0, 0, 0], [0, 0, 0, 0, 0, 8, 2, 0, 0, 8, 0, 0, 0], [0, 0, 8, 0, 0, 8, 2, 2, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 8, 2, 0, 0, 8, 2, 0, 0], [0, 0, 8, 0, 0, 8, 2, 0, 0, 8, 2, 2, 2], [0, 0, 8, 0, 0, 8, 2, 2, 2, 2, 2, 0, 0], [0, 0, 8, 0, 0, 8, 2, 0, 0, 8, 0, 0, 0], [0, 0, 8, 0, 0, 8, 2, 0, 0, 8, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 0, 0, 8, 0, 0, 0], [0, 0, 8, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 8, 0, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 0, 8, 8, 0, 8, 8, 8, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [8, 8, 8, 8, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [8, 8, 2, 8, 8, 8, 8, 2, 8, 8, 8, 0, 8, 8], [0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0], [8, 8, 2, 8, 8, 8, 8, 8, 8, 2, 8, 8, 8, 8], [0, 0, 2, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0], [2, 2, 2, 2, 2, 0, 0, 2, 2, 2, 2, 2, 0, 0], [8, 8, 8, 8, 2, 8, 8, 2, 8, 8, 8, 2, 8, 8], [0, 0, 0, 0, 2, 0, 0, 2, 0, 0, 0, 2, 0, 0], [0, 0, 0, 0, 2, 0, 0, 2, 0, 0, 0, 2, 0, 0], [0, 0, 0, 0, 2, 0, 0, 2, 0, 0, 0, 2, 0, 0], [0, 0, 0, 0, 2, 0, 0, 2, 0, 0, 0, 2, 0, 0]]}]}