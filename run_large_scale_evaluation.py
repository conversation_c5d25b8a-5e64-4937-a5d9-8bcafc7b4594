#!/usr/bin/env python3
"""
Run Large-Scale ARC-AGI-2 Evaluation

This script runs the ARC solver on the training dataset to evaluate performance
and identify areas for improvement.

Usage:
    python run_large_scale_evaluation.py --max_puzzles 100 --save_results
"""

import argparse
import sys
import time
from pathlib import Path

# Add the current directory to Python path to import our modules
sys.path.append(str(Path(__file__).parent))

from arc_large_scale_evaluation import ARCDatasetLoader, LargeScaleEvaluator
import logging

# Import the solver components from the notebook
# Note: In practice, you'd extract these to separate modules
try:
    # Try to import from a converted notebook module
    from arc_solver_components import *
except ImportError:
    print("Error: Could not import solver components.")
    print("Please run the notebook cells first or extract the solver classes to arc_solver_components.py")
    sys.exit(1)

def setup_logging(verbose: bool = False):
    """Set up logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('evaluation.log'),
            logging.StreamHandler()
        ]
    )

def create_solver_from_training_data(training_data: dict, max_feature_puzzles: int = 50):
    """
    Create a solver instance with feature database built from training data.
    
    Args:
        training_data: Training dataset dictionary
        max_feature_puzzles: Maximum puzzles to use for feature database
        
    Returns:
        Configured solver instance
    """
    logger = logging.getLogger(__name__)
    logger.info("Building feature database from training data...")
    
    # Build feature database from a subset of training data
    feature_puzzles = dict(list(training_data.items())[:max_feature_puzzles])
    feature_db = build_feature_database(feature_puzzles, max_puzzles=max_feature_puzzles)
    
    # Create solver with feature database
    solver = HybridARCSolver(
        feature_db=feature_db,
        max_retrieval_candidates=5,
        max_transformation_depth=3
    )
    
    logger.info(f"Solver created with feature database of {len(feature_puzzles)} puzzles")
    return solver

def main():
    parser = argparse.ArgumentParser(description='Run large-scale ARC evaluation')
    parser.add_argument('--max_puzzles', type=int, default=100,
                       help='Maximum number of puzzles to evaluate (default: 100)')
    parser.add_argument('--max_feature_puzzles', type=int, default=50,
                       help='Maximum puzzles for feature database (default: 50)')
    parser.add_argument('--data_dir', type=str, default='ARC-AGI-2/data',
                       help='Path to ARC data directory')
    parser.add_argument('--save_results', action='store_true',
                       help='Save detailed results to file')
    parser.add_argument('--results_file', type=str, default='evaluation_results.pkl',
                       help='File to save results to')
    parser.add_argument('--generate_report', action='store_true',
                       help='Generate performance analysis report')
    parser.add_argument('--report_dir', type=str, default='performance_analysis',
                       help='Directory for performance report')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed for reproducibility')
    
    args = parser.parse_args()
    
    # Set up logging
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    logger.info("Starting ARC-AGI-2 Large-Scale Evaluation")
    logger.info(f"Configuration: max_puzzles={args.max_puzzles}, "
               f"max_feature_puzzles={args.max_feature_puzzles}")
    
    try:
        # Load datasets
        loader = ARCDatasetLoader(args.data_dir)
        
        # Load training data for both feature database and evaluation
        logger.info("Loading training dataset...")
        training_data = loader.load_training_dataset(
            max_puzzles=args.max_puzzles + args.max_feature_puzzles,
            shuffle=True,
            seed=args.seed
        )
        
        if len(training_data) < args.max_feature_puzzles:
            logger.warning(f"Only {len(training_data)} puzzles available, "
                          f"adjusting feature database size")
            args.max_feature_puzzles = min(args.max_feature_puzzles, len(training_data) // 2)
        
        # Split data: first part for feature database, rest for evaluation
        all_puzzle_ids = list(training_data.keys())
        feature_puzzle_ids = all_puzzle_ids[:args.max_feature_puzzles]
        eval_puzzle_ids = all_puzzle_ids[args.max_feature_puzzles:args.max_feature_puzzles + args.max_puzzles]
        
        feature_data = {pid: training_data[pid] for pid in feature_puzzle_ids}
        eval_data = {pid: training_data[pid] for pid in eval_puzzle_ids}
        
        logger.info(f"Using {len(feature_data)} puzzles for feature database")
        logger.info(f"Evaluating on {len(eval_data)} puzzles")
        
        # Create solver
        solver = create_solver_from_training_data(feature_data, args.max_feature_puzzles)
        
        # Create evaluator
        evaluator = LargeScaleEvaluator(solver, max_runtime_per_puzzle=30.0)
        
        # Run evaluation
        logger.info("Starting evaluation...")
        start_time = time.time()
        
        stats = evaluator.evaluate_dataset(
            eval_data,
            max_puzzles=args.max_puzzles,
            save_results=args.save_results,
            results_file=args.results_file
        )
        
        total_time = time.time() - start_time
        
        # Print summary
        print("\n" + "="*60)
        print("EVALUATION SUMMARY")
        print("="*60)
        print(f"Total puzzles evaluated: {stats.total_puzzles}")
        print(f"Puzzles solved: {stats.solved_puzzles}")
        print(f"Solve rate: {stats.solve_rate:.1%}")
        print(f"Average partial score: {stats.avg_partial_score:.3f}")
        print(f"Average runtime per puzzle: {stats.avg_runtime:.2f}s")
        print(f"Total evaluation time: {total_time/60:.1f} minutes")
        print(f"Method breakdown:")
        for method, count in stats.method_breakdown.items():
            print(f"  {method}: {count} ({100*count/stats.total_puzzles:.1f}%)")
        
        # Performance assessment
        print(f"\nPERFORMANCE ASSESSMENT:")
        if stats.solve_rate >= 0.15:
            print(f"✓ Meets minimum target (≥15% solve rate)")
        else:
            print(f"✗ Below minimum target (<15% solve rate)")
        
        if stats.avg_runtime < 10.0:
            print(f"✓ Good runtime performance (<10s average)")
        elif stats.avg_runtime < 30.0:
            print(f"~ Acceptable runtime performance (<30s average)")
        else:
            print(f"✗ Slow runtime performance (≥30s average)")
        
        # Kaggle compliance check
        estimated_full_runtime = stats.avg_runtime * 400  # Estimate for 400 puzzles
        if estimated_full_runtime < 12 * 3600:  # 12 hours
            print(f"✓ Kaggle compliant (estimated {estimated_full_runtime/3600:.1f}h for 400 puzzles)")
        else:
            print(f"✗ May exceed Kaggle time limit (estimated {estimated_full_runtime/3600:.1f}h)")
        
        # Generate report if requested
        if args.generate_report:
            logger.info("Generating performance report...")
            evaluator.generate_performance_report(args.report_dir)
            print(f"\nDetailed performance report saved to {args.report_dir}/")
        
        # Recommendations
        print(f"\nRECOMMENDATIONS:")
        if stats.solve_rate < 0.10:
            print("- Focus on improving basic transformation coverage")
            print("- Add more domain-specific rules")
            print("- Improve feature extraction for better retrieval")
        elif stats.solve_rate < 0.20:
            print("- Fine-tune transformation search parameters")
            print("- Add more sophisticated pattern recognition")
            print("- Optimize retrieval similarity metrics")
        else:
            print("- Focus on runtime optimization")
            print("- Add early stopping mechanisms")
            print("- Consider ensemble methods for difficult cases")
        
        if stats.avg_runtime > 20.0:
            print("- Implement transformation caching")
            print("- Add timeout mechanisms for complex searches")
            print("- Optimize feature computation")
        
        logger.info("Evaluation completed successfully")
        
    except Exception as e:
        logger.error(f"Evaluation failed: {e}")
        raise

if __name__ == "__main__":
    main()
