{"train": [{"input": [[8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2], [0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8], [8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2], [0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8], [8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2, 3, 0, 8, 8, 8, 2], [0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2], [0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8], [8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2, 3, 0, 8, 2, 8, 2], [0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8, 3, 0, 8, 8, 8, 8], [8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4, 3, 8, 8, 8, 8, 4]], "output": [[8, 8, 4, 8, 8], [8, 8, 8, 8, 8], [0, 8, 1, 8, 2], [1, 8, 8, 8, 8], [8, 8, 8, 8, 4]]}, {"input": [[0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 8, 2, 8, 4, 3, 0, 8, 8, 8, 4, 3, 0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [8, 8, 0, 8, 2, 3, 8, 8, 8, 8, 8, 3, 8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4, 3, 0, 8, 2, 8, 4], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8, 3, 4, 8, 6, 8, 8], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2, 3, 8, 8, 0, 8, 2]], "output": [[0, 8, 1, 8, 4], [8, 8, 8, 8, 8], [4, 8, 6, 8, 8], [8, 8, 8, 8, 8], [8, 8, 1, 8, 1]]}, {"input": [[0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8], [4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8], [8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8], [8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8], [0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8], [4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8], [8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8], [8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8], [0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8], [4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8], [8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8], [8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8], [0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8], [4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8], [8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8], [8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8], [0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8, 3, 0, 8, 8, 8, 8, 3, 0, 8, 4, 8, 8, 3, 0, 8, 4, 8, 8], [4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8, 3, 4, 8, 8, 8, 8], [8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 2, 8, 8, 8, 3, 8, 2, 8, 8, 8], [8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 2, 8, 3, 8, 8, 8, 2, 8], [0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0, 3, 0, 8, 8, 8, 0]], "output": [[0, 8, 1, 8, 8], [1, 8, 8, 8, 8], [8, 1, 8, 8, 8], [8, 8, 8, 1, 8], [0, 8, 8, 8, 0]]}], "test": [{"input": [[2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8], [8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8], [2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8], [8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6, 3, 8, 8, 8, 8, 8, 3, 8, 0, 0, 8, 6], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8], [2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8], [8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8], [2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8], [8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8], [2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8, 3, 2, 8, 2, 8, 8], [8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6, 3, 8, 0, 0, 8, 6], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8, 3, 8, 8, 4, 8, 8], [2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4, 3, 2, 8, 8, 8, 4]], "output": [[2, 8, 2, 8, 8], [8, 1, 1, 8, 1], [8, 8, 8, 8, 8], [8, 8, 4, 8, 8], [2, 8, 8, 8, 4]]}]}