{"train": [{"input": [[7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 6, 6, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 6, 1, 1, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 6, 1, 1, 3, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 6, 1, 1, 3, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 6, 1, 3, 9, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7], [8, 7, 7, 6, 1, 3, 9, 9, 8, 7, 7, 7, 7, 7, 7, 7, 8], [7, 7, 6, 1, 1, 3, 9, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 6, 1, 3, 9, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 6, 1, 1, 3, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 6, 1, 1, 3, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 6, 1, 6, 1, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 6, 7, 6, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7]], "output": [[7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 6, 7, 6, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 6, 6, 4, 1, 6, 1, 6, 7, 7, 7, 7], [7, 7, 7, 7, 7, 6, 1, 1, 4, 3, 1, 1, 6, 7, 7, 7, 7], [7, 7, 7, 7, 6, 1, 1, 3, 4, 9, 3, 1, 1, 6, 7, 7, 7], [7, 7, 7, 6, 1, 1, 3, 9, 4, 9, 9, 3, 1, 6, 7, 7, 7], [7, 7, 7, 6, 1, 3, 9, 9, 4, 9, 9, 3, 1, 1, 6, 7, 7], [8, 7, 7, 6, 1, 3, 9, 9, 8, 9, 9, 3, 1, 6, 7, 7, 8], [7, 7, 6, 1, 1, 3, 9, 9, 4, 9, 9, 3, 1, 6, 7, 7, 7], [7, 7, 7, 6, 1, 3, 9, 9, 4, 9, 3, 1, 1, 6, 7, 7, 7], [7, 7, 7, 6, 1, 1, 3, 9, 4, 3, 1, 1, 6, 7, 7, 7, 7], [7, 7, 7, 7, 6, 1, 1, 3, 4, 1, 1, 6, 7, 7, 7, 7, 7], [7, 7, 7, 7, 6, 1, 6, 1, 4, 6, 6, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 6, 7, 6, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7]]}, {"input": [[7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 1, 1, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 1, 2, 2, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 1, 2, 9, 9, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 1, 2, 9, 9, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 1, 2, 9, 9, 9, 4, 7, 7, 7, 7, 7, 7, 7], [8, 7, 7, 1, 2, 9, 9, 8, 7, 7, 7, 7, 7, 7, 8], [7, 7, 7, 1, 2, 9, 9, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 1, 2, 9, 9, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 1, 2, 2, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 1, 1, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7]], "output": [[7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 1, 1, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 1, 2, 2, 4, 1, 1, 7, 7, 7, 7, 7], [7, 7, 7, 1, 2, 9, 9, 4, 2, 2, 1, 7, 7, 7, 7], [7, 7, 7, 1, 2, 9, 9, 4, 9, 9, 2, 1, 7, 7, 7], [7, 7, 1, 2, 9, 9, 9, 4, 9, 9, 2, 1, 7, 7, 7], [8, 7, 7, 1, 2, 9, 9, 8, 9, 9, 2, 1, 7, 7, 8], [7, 7, 7, 1, 2, 9, 9, 4, 9, 9, 9, 2, 1, 7, 7], [7, 7, 7, 1, 2, 9, 9, 4, 9, 9, 2, 1, 7, 7, 7], [7, 7, 7, 7, 1, 2, 2, 4, 9, 9, 2, 1, 7, 7, 7], [7, 7, 7, 7, 7, 1, 1, 4, 2, 2, 1, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 4, 1, 1, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7]]}, {"input": [[7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 5, 5, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 5, 5, 9, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7], [8, 7, 5, 5, 9, 9, 1, 1, 8, 7, 7, 7, 7, 7, 7, 7, 8], [7, 7, 5, 9, 1, 1, 1, 1, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 5, 9, 1, 1, 1, 1, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 5, 9, 6, 6, 6, 6, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 5, 9, 6, 6, 6, 6, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 5, 5, 9, 9, 6, 6, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 5, 5, 9, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 5, 5, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7]], "output": [[7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 5, 5, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 9, 9, 5, 5, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 6, 6, 9, 9, 5, 5, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 6, 6, 6, 6, 9, 5, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 6, 6, 6, 6, 9, 5, 7, 7], [7, 7, 7, 7, 7, 7, 5, 5, 4, 1, 1, 1, 1, 9, 5, 7, 7], [7, 7, 7, 7, 5, 5, 9, 9, 4, 1, 1, 1, 1, 9, 5, 7, 7], [8, 7, 5, 5, 9, 9, 1, 1, 8, 1, 1, 9, 9, 5, 5, 7, 8], [7, 7, 5, 9, 1, 1, 1, 1, 4, 9, 9, 5, 5, 7, 7, 7, 7], [7, 7, 5, 9, 1, 1, 1, 1, 4, 5, 5, 7, 7, 7, 7, 7, 7], [7, 7, 5, 9, 6, 6, 6, 6, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 5, 9, 6, 6, 6, 6, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 5, 5, 9, 9, 6, 6, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 5, 5, 9, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 5, 5, 4, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7]]}], "test": [{"input": [[7, 7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 3, 3, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 3, 3, 3, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 3, 0, 0, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 3, 3, 0, 0, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 3, 3, 2, 2, 2, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 3, 3, 2, 2, 2, 2, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 3, 2, 2, 2, 2, 2, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [8, 7, 3, 5, 5, 5, 5, 5, 9, 8, 7, 7, 7, 7, 7, 7, 7, 7, 8], [7, 7, 3, 5, 5, 5, 5, 5, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 3, 1, 1, 1, 1, 1, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 3, 1, 1, 1, 1, 1, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 3, 3, 1, 1, 1, 1, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 3, 3, 6, 6, 6, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 3, 3, 3, 7, 9, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 3, 3, 3, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7]], "output": [[7, 7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 3, 3, 4, 3, 3, 3, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 3, 3, 3, 9, 4, 9, 7, 3, 3, 3, 7, 7, 7, 7], [7, 7, 7, 7, 7, 3, 0, 0, 9, 4, 9, 6, 6, 6, 3, 3, 7, 7, 7], [7, 7, 7, 7, 3, 3, 0, 0, 9, 4, 9, 1, 1, 1, 1, 3, 3, 7, 7], [7, 7, 7, 3, 3, 2, 2, 2, 9, 4, 9, 1, 1, 1, 1, 1, 3, 7, 7], [7, 7, 3, 3, 2, 2, 2, 2, 9, 4, 9, 1, 1, 1, 1, 1, 3, 7, 7], [7, 7, 3, 2, 2, 2, 2, 2, 9, 4, 9, 5, 5, 5, 5, 5, 3, 7, 7], [8, 7, 3, 5, 5, 5, 5, 5, 9, 8, 9, 5, 5, 5, 5, 5, 3, 7, 8], [7, 7, 3, 5, 5, 5, 5, 5, 9, 4, 9, 2, 2, 2, 2, 2, 3, 7, 7], [7, 7, 3, 1, 1, 1, 1, 1, 9, 4, 9, 2, 2, 2, 2, 3, 3, 7, 7], [7, 7, 3, 1, 1, 1, 1, 1, 9, 4, 9, 2, 2, 2, 3, 3, 7, 7, 7], [7, 7, 3, 3, 1, 1, 1, 1, 9, 4, 9, 0, 0, 3, 3, 7, 7, 7, 7], [7, 7, 7, 3, 3, 6, 6, 6, 9, 4, 9, 0, 0, 3, 7, 7, 7, 7, 7], [7, 7, 7, 7, 3, 3, 3, 7, 9, 4, 9, 3, 3, 3, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 3, 3, 3, 4, 3, 3, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7]]}]}