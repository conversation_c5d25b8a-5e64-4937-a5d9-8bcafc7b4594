{"train": [{"input": [[0, 0, 0, 0, 0, 7, 0, 7, 0, 0], [0, 0, 0, 0, 0, 7, 0, 7, 0, 0], [0, 0, 0, 7, 7, 7, 0, 7, 0, 0], [0, 0, 0, 7, 0, 0, 0, 7, 0, 0], [0, 0, 0, 7, 0, 7, 7, 7, 0, 0], [0, 0, 0, 7, 0, 7, 0, 0, 0, 0], [0, 0, 0, 7, 0, 7, 0, 0, 0, 0], [0, 0, 0, 7, 9, 7, 0, 0, 0, 0], [0, 0, 0, 7, 0, 7, 0, 0, 0, 0], [0, 0, 0, 7, 0, 7, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 7, 8, 7, 0, 0], [0, 0, 0, 0, 0, 7, 8, 7, 0, 0], [0, 0, 0, 7, 7, 7, 8, 7, 0, 0], [0, 0, 0, 7, 8, 8, 8, 7, 0, 0], [0, 0, 0, 7, 8, 7, 7, 7, 0, 0], [0, 0, 0, 7, 8, 7, 0, 0, 0, 0], [0, 0, 0, 7, 8, 7, 0, 0, 0, 0], [0, 0, 0, 7, 8, 7, 0, 0, 0, 0], [0, 0, 0, 7, 8, 7, 0, 0, 0, 0], [0, 0, 0, 7, 8, 7, 0, 0, 0, 0]]}, {"input": [[7, 0, 7, 0, 0, 0, 0, 0, 0, 0], [7, 0, 7, 0, 0, 0, 0, 0, 0, 0], [7, 0, 7, 7, 7, 7, 0, 0, 0, 0], [7, 0, 0, 0, 0, 7, 0, 0, 0, 0], [7, 7, 7, 7, 0, 7, 0, 0, 0, 0], [0, 0, 0, 7, 0, 7, 7, 7, 7, 0], [0, 0, 0, 7, 9, 0, 0, 0, 7, 0], [0, 0, 0, 7, 7, 7, 7, 0, 7, 0], [0, 0, 0, 0, 0, 0, 7, 0, 7, 0], [0, 0, 0, 0, 0, 0, 7, 0, 7, 0]], "output": [[7, 8, 7, 0, 0, 0, 0, 0, 0, 0], [7, 8, 7, 0, 0, 0, 0, 0, 0, 0], [7, 8, 7, 7, 7, 7, 0, 0, 0, 0], [7, 8, 8, 8, 8, 7, 0, 0, 0, 0], [7, 7, 7, 7, 8, 7, 0, 0, 0, 0], [0, 0, 0, 7, 8, 7, 7, 7, 7, 0], [0, 0, 0, 7, 9, 0, 0, 0, 7, 0], [0, 0, 0, 7, 7, 7, 7, 0, 7, 0], [0, 0, 0, 0, 0, 0, 7, 0, 7, 0], [0, 0, 0, 0, 0, 0, 7, 0, 7, 0]]}, {"input": [[0, 0, 7, 0, 7, 0, 0, 0, 0, 0], [0, 0, 7, 0, 7, 0, 0, 0, 0, 0], [0, 0, 7, 0, 7, 7, 7, 0, 0, 0], [0, 0, 7, 0, 0, 0, 7, 0, 0, 0], [0, 0, 7, 7, 7, 9, 7, 0, 0, 0], [0, 0, 0, 0, 7, 0, 7, 0, 0, 0], [0, 0, 0, 0, 7, 0, 7, 7, 7, 0], [0, 0, 0, 0, 7, 0, 0, 0, 7, 0], [0, 0, 0, 0, 7, 7, 7, 0, 7, 0], [0, 0, 0, 0, 0, 0, 7, 0, 7, 0]], "output": [[0, 0, 7, 8, 7, 0, 0, 0, 0, 0], [0, 0, 7, 8, 7, 0, 0, 0, 0, 0], [0, 0, 7, 8, 7, 7, 7, 0, 0, 0], [0, 0, 7, 8, 8, 8, 7, 0, 0, 0], [0, 0, 7, 7, 7, 8, 7, 0, 0, 0], [0, 0, 0, 0, 7, 8, 7, 0, 0, 0], [0, 0, 0, 0, 7, 8, 7, 7, 7, 0], [0, 0, 0, 0, 7, 8, 8, 8, 7, 0], [0, 0, 0, 0, 7, 7, 7, 8, 7, 0], [0, 0, 0, 0, 0, 0, 7, 8, 7, 0]]}, {"input": [[0, 0, 0, 7, 0, 7, 0, 0, 0, 0], [0, 0, 0, 7, 0, 7, 0, 0, 0, 0], [0, 0, 0, 7, 0, 7, 7, 7, 0, 0], [0, 0, 0, 7, 0, 0, 0, 7, 0, 0], [0, 0, 0, 7, 7, 7, 0, 7, 0, 0], [0, 0, 0, 0, 0, 7, 0, 7, 0, 0], [0, 0, 0, 0, 0, 7, 0, 7, 0, 0], [0, 0, 0, 0, 0, 7, 0, 7, 7, 7], [0, 0, 0, 0, 0, 7, 0, 0, 0, 0], [0, 0, 0, 0, 0, 7, 7, 7, 7, 7]], "output": [[0, 0, 0, 7, 8, 7, 0, 0, 0, 0], [0, 0, 0, 7, 8, 7, 0, 0, 0, 0], [0, 0, 0, 7, 8, 7, 7, 7, 0, 0], [0, 0, 0, 7, 8, 8, 8, 7, 0, 0], [0, 0, 0, 7, 7, 7, 8, 7, 0, 0], [0, 0, 0, 0, 0, 7, 8, 7, 0, 0], [0, 0, 0, 0, 0, 7, 8, 7, 0, 0], [0, 0, 0, 0, 0, 7, 8, 7, 7, 7], [0, 0, 0, 0, 0, 7, 8, 8, 8, 8], [0, 0, 0, 0, 0, 7, 7, 7, 7, 7]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 7, 0, 7], [0, 0, 0, 0, 0, 0, 0, 7, 0, 7], [0, 0, 0, 0, 7, 7, 7, 7, 0, 7], [0, 0, 0, 0, 7, 0, 0, 0, 0, 7], [0, 0, 0, 0, 7, 0, 7, 7, 7, 7], [7, 7, 7, 7, 7, 0, 7, 0, 0, 0], [7, 0, 0, 0, 0, 9, 7, 0, 0, 0], [7, 0, 7, 7, 7, 7, 7, 0, 0, 0], [7, 0, 7, 0, 0, 0, 0, 0, 0, 0], [7, 0, 7, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 7, 8, 7], [0, 0, 0, 0, 0, 0, 0, 7, 8, 7], [0, 0, 0, 0, 7, 7, 7, 7, 8, 7], [0, 0, 0, 0, 7, 8, 8, 8, 8, 7], [0, 0, 0, 0, 7, 8, 7, 7, 7, 7], [7, 7, 7, 7, 7, 8, 7, 0, 0, 0], [7, 0, 0, 0, 0, 9, 7, 0, 0, 0], [7, 0, 7, 7, 7, 7, 7, 0, 0, 0], [7, 0, 7, 0, 0, 0, 0, 0, 0, 0], [7, 0, 7, 0, 0, 0, 0, 0, 0, 0]]}]}