{"train": [{"input": [[5, 7, 5, 7, 5, 7, 7, 5], [5, 7, 5, 5, 7, 7, 5, 7], [5, 7, 7, 7, 7, 5, 5, 7], [5, 7, 7, 7, 5, 7, 5, 7], [5, 7, 7, 7, 7, 7, 5, 7], [5, 5, 5, 7, 7, 7, 7, 7]], "output": [[5, 7, 5, 7, 5, 7, 7, 8], [5, 7, 5, 5, 7, 7, 8, 7], [5, 7, 7, 7, 7, 8, 8, 7], [5, 7, 7, 7, 8, 7, 8, 7], [5, 7, 7, 7, 7, 7, 8, 7], [5, 5, 5, 7, 7, 7, 7, 7]]}, {"input": [[7, 7, 7, 5, 7], [7, 5, 5, 7, 7], [5, 5, 7, 7, 7], [7, 7, 7, 5, 5]], "output": [[7, 7, 7, 8, 7], [7, 8, 8, 7, 7], [8, 8, 7, 7, 7], [7, 7, 7, 5, 5]]}, {"input": [[7, 7, 7, 7, 5, 5, 7, 7, 5, 7, 7], [5, 5, 7, 7, 5, 5, 7, 7, 7, 5, 5], [5, 5, 7, 7, 7, 5, 7, 5, 7, 7, 5], [5, 5, 7, 7, 7, 7, 7, 7, 7, 7, 7], [5, 5, 7, 7, 7, 5, 5, 7, 7, 7, 5], [7, 7, 7, 7, 7, 7, 5, 7, 7, 5, 7], [7, 5, 7, 5, 5, 7, 5, 7, 5, 7, 7], [7, 7, 7, 7, 5, 7, 5, 7, 7, 7, 7], [5, 7, 7, 7, 7, 7, 7, 7, 7, 7, 5], [5, 5, 7, 7, 5, 7, 5, 7, 5, 7, 7], [7, 5, 5, 7, 7, 5, 7, 5, 7, 5, 7], [7, 5, 5, 5, 7, 7, 7, 7, 7, 7, 7]], "output": [[7, 7, 7, 7, 5, 5, 7, 7, 8, 7, 7], [5, 5, 7, 7, 5, 5, 7, 7, 7, 8, 8], [5, 5, 7, 7, 7, 5, 7, 5, 7, 7, 8], [5, 5, 7, 7, 7, 7, 7, 7, 7, 7, 7], [5, 5, 7, 7, 7, 5, 5, 7, 7, 7, 8], [7, 7, 7, 7, 7, 7, 5, 7, 7, 8, 7], [7, 5, 7, 5, 5, 7, 5, 7, 8, 7, 7], [7, 7, 7, 7, 5, 7, 5, 7, 7, 7, 7], [8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 5], [8, 8, 7, 7, 5, 7, 5, 7, 5, 7, 7], [7, 8, 8, 7, 7, 5, 7, 5, 7, 5, 7], [7, 8, 8, 8, 7, 7, 7, 7, 7, 7, 7]]}], "test": [{"input": [[7, 5, 5, 7, 7, 7, 7, 7, 5, 5, 5, 5, 7, 7, 5, 7, 7, 7, 7], [7, 7, 5, 7, 7, 7, 7, 7, 7, 7, 7, 5, 5, 5, 7, 7, 5, 7, 7], [7, 7, 5, 7, 7, 5, 7, 5, 7, 5, 7, 7, 7, 7, 7, 5, 5, 7, 7], [5, 7, 7, 5, 7, 7, 7, 5, 5, 7, 7, 7, 7, 5, 7, 5, 7, 5, 7], [7, 7, 7, 5, 7, 5, 7, 7, 7, 5, 7, 7, 7, 7, 7, 7, 7, 5, 5], [7, 5, 7, 5, 7, 7, 7, 7, 7, 7, 5, 7, 7, 7, 7, 7, 7, 5, 5], [7, 5, 5, 7, 5, 7, 7, 7, 7, 7, 7, 7, 7, 7, 5, 5, 7, 7, 5], [7, 7, 7, 7, 7, 7, 5, 5, 7, 7, 7, 5, 5, 7, 7, 7, 7, 5, 7], [7, 7, 5, 7, 7, 5, 5, 7, 5, 5, 7, 7, 5, 5, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 5, 5, 7, 7, 5, 7, 7, 7, 7, 7, 5, 7, 7], [5, 7, 7, 7, 7, 7, 7, 7, 7, 7, 5, 7, 5, 7, 7, 7, 7, 5, 5], [7, 5, 5, 5, 7, 7, 7, 7, 5, 7, 7, 7, 7, 7, 7, 7, 7, 5, 7], [7, 5, 7, 7, 7, 7, 7, 7, 5, 5, 7, 7, 7, 7, 5, 7, 7, 5, 5], [7, 5, 5, 7, 7, 7, 5, 7, 5, 5, 7, 5, 5, 5, 7, 5, 7, 7, 7], [7, 7, 7, 7, 7, 5, 5, 7, 5, 7, 7, 7, 7, 7, 5, 5, 5, 7, 5], [7, 7, 7, 7, 7, 7, 5, 5, 7, 7, 7, 7, 7, 7, 5, 7, 7, 7, 7]], "output": [[7, 5, 5, 7, 7, 7, 7, 7, 5, 5, 5, 5, 7, 7, 5, 7, 7, 7, 7], [7, 7, 5, 7, 7, 7, 7, 7, 7, 7, 7, 5, 5, 5, 7, 7, 8, 7, 7], [7, 7, 5, 7, 7, 5, 7, 8, 7, 8, 7, 7, 7, 7, 7, 8, 8, 7, 7], [5, 7, 7, 5, 7, 7, 7, 8, 8, 7, 7, 7, 7, 5, 7, 8, 7, 8, 7], [7, 7, 7, 5, 7, 5, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 8, 8], [7, 5, 7, 5, 7, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 8, 8], [7, 5, 5, 7, 5, 7, 7, 7, 7, 7, 7, 7, 7, 7, 5, 5, 7, 7, 8], [7, 7, 7, 7, 7, 7, 5, 5, 7, 7, 7, 5, 5, 7, 7, 7, 7, 8, 7], [7, 7, 5, 7, 7, 5, 5, 7, 5, 5, 7, 7, 5, 5, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 7, 5, 5, 7, 7, 5, 7, 7, 7, 7, 7, 5, 7, 7], [5, 7, 7, 7, 7, 7, 7, 7, 7, 7, 5, 7, 5, 7, 7, 7, 7, 5, 5], [7, 5, 5, 5, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 5, 7], [7, 5, 7, 7, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 8, 7, 7, 5, 5], [7, 5, 5, 7, 7, 7, 8, 7, 8, 8, 7, 8, 8, 8, 7, 8, 7, 7, 7], [7, 7, 7, 7, 7, 8, 8, 7, 8, 7, 7, 7, 7, 7, 8, 8, 8, 7, 5], [7, 7, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7]]}]}