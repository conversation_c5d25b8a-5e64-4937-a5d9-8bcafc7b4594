{"train": [{"input": [[9, 9, 9, 6, 6, 6, 6, 9, 4], [9, 9, 9, 6, 6, 6, 4, 4, 6], [9, 9, 9, 6, 6, 9, 6, 6, 6], [6, 6, 6, 6, 6, 6, 6, 6, 6], [6, 6, 9, 6, 6, 6, 6, 6, 6], [6, 9, 6, 6, 6, 6, 6, 6, 6], [9, 6, 6, 6, 6, 6, 4, 4, 4], [6, 6, 4, 6, 6, 6, 4, 4, 4], [6, 6, 6, 4, 6, 6, 4, 4, 4]], "output": [[9, 9, 9, 9, 6, 6, 6, 6, 6], [9, 9, 9, 6, 6, 6, 6, 6, 6], [9, 9, 9, 9, 6, 6, 6, 6, 6], [9, 9, 9, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 4, 4, 4], [6, 6, 6, 6, 6, 6, 4, 4, 4], [6, 6, 6, 6, 6, 4, 4, 4, 4], [6, 6, 6, 6, 6, 4, 4, 4, 4]]}, {"input": [[2, 2, 2, 6, 6, 6, 6, 6, 2], [2, 2, 2, 6, 6, 6, 6, 6, 2], [2, 2, 2, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 2, 6, 6, 6, 6], [6, 2, 6, 6, 6, 6, 6, 5, 6], [6, 6, 6, 6, 5, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 5, 5, 5], [5, 6, 6, 6, 6, 6, 5, 5, 5], [6, 6, 2, 6, 6, 6, 5, 5, 5]], "output": [[2, 2, 2, 2, 6, 6, 6, 6, 6], [2, 2, 2, 2, 6, 6, 6, 6, 6], [2, 2, 2, 2, 6, 6, 6, 6, 6], [6, 2, 2, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 6, 5, 6], [6, 6, 6, 6, 6, 5, 5, 5, 5], [6, 6, 6, 6, 6, 5, 5, 5, 5], [6, 6, 6, 6, 6, 6, 5, 5, 5]]}], "test": [{"input": [[3, 3, 3, 6, 6, 6, 3, 6, 6], [3, 3, 3, 6, 3, 6, 6, 6, 6], [3, 3, 3, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 8, 8, 8], [6, 6, 6, 6, 6, 6, 6, 6, 6], [6, 3, 6, 6, 8, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 8, 8, 8], [6, 8, 6, 6, 6, 6, 8, 8, 8], [6, 6, 6, 8, 6, 6, 8, 8, 8]], "output": [[3, 3, 3, 3, 6, 6, 6, 6, 6], [3, 3, 3, 3, 6, 6, 6, 6, 6], [3, 3, 3, 6, 6, 6, 6, 6, 6], [6, 3, 6, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6, 8, 8, 8], [6, 6, 6, 6, 6, 8, 8, 8, 8], [6, 6, 6, 6, 6, 8, 8, 8, 8], [6, 6, 6, 6, 6, 8, 8, 8, 8]]}]}