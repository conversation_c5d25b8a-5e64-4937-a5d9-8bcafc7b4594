{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 1, 1, 3, 3, 3, 2, 3, 8, 3], [3, 1, 1, 3, 3, 3, 2, 3, 8, 3], [3, 3, 3, 3, 3, 3, 2, 3, 8, 3], [3, 3, 2, 2, 3, 3, 3, 3, 8, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 8, 8, 3, 3, 3, 3, 3, 3], [3, 3, 8, 8, 3, 4, 4, 4, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 2, 2, 3, 3, 3, 3, 3, 3, 3], [3, 2, 2, 3, 3, 3, 3, 2, 2, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 2, 3, 8, 3], [3, 3, 3, 3, 3, 3, 2, 3, 8, 3], [3, 3, 3, 3, 3, 3, 2, 3, 8, 3], [3, 3, 2, 2, 3, 3, 3, 3, 8, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 8, 8, 3, 3, 3, 3, 3, 3], [3, 3, 8, 8, 3, 4, 4, 4, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 2, 2, 3, 3, 3, 3, 3, 3, 3], [3, 2, 2, 3, 3, 3, 3, 2, 2, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 2, 2, 1, 1, 1, 1, 1, 1, 1], [1, 2, 1, 1, 1, 2, 2, 1, 3, 1], [1, 1, 1, 1, 1, 2, 2, 1, 3, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 8, 1, 1, 3, 3, 1, 1, 1], [1, 1, 8, 1, 1, 3, 3, 1, 1, 1], [1, 1, 8, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 3, 3, 3, 3, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 3, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 2, 2, 1, 1, 1, 1, 1, 1, 1], [1, 2, 1, 1, 1, 2, 2, 1, 3, 1], [1, 1, 1, 1, 1, 2, 2, 1, 3, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 3, 3, 1, 1, 1], [1, 1, 1, 1, 1, 3, 3, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 3, 3, 3, 3, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 2, 2, 8, 8, 8, 8, 8, 4, 8], [8, 8, 8, 8, 8, 8, 1, 8, 4, 8], [8, 6, 8, 4, 4, 8, 1, 8, 8, 8], [8, 6, 8, 4, 4, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 4, 8, 2, 2, 8, 8, 3, 3, 8], [8, 4, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 6, 8, 8, 8], [8, 8, 1, 8, 8, 6, 6, 8, 8, 8], [8, 8, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 4, 4, 4, 4, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 4, 4, 4, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 2, 2, 8, 8, 8, 8, 8, 4, 8], [8, 8, 8, 8, 8, 8, 1, 8, 4, 8], [8, 8, 8, 4, 4, 8, 1, 8, 8, 8], [8, 8, 8, 4, 4, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 4, 8, 2, 2, 8, 8, 8, 8, 8], [8, 4, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 4, 4, 4, 4, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 2, 2, 2, 4, 4, 3, 3, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 7, 7, 4, 4, 4, 2, 2, 4, 4], [4, 4, 4, 4, 4, 4, 2, 2, 4, 4], [4, 3, 3, 4, 4, 4, 4, 4, 4, 4], [4, 3, 3, 4, 4, 4, 4, 4, 2, 4], [4, 4, 4, 4, 5, 5, 4, 4, 2, 4], [4, 4, 4, 4, 5, 5, 4, 4, 2, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 2, 2, 4, 4, 4, 4, 4, 4, 4], [4, 2, 2, 4, 4, 4, 2, 2, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 2, 2, 2, 4, 4, 3, 3, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 2, 2, 4, 4], [4, 4, 4, 4, 4, 4, 2, 2, 4, 4], [4, 3, 3, 4, 4, 4, 4, 4, 4, 4], [4, 3, 3, 4, 4, 4, 4, 4, 2, 4], [4, 4, 4, 4, 4, 4, 4, 4, 2, 4], [4, 4, 4, 4, 4, 4, 4, 4, 2, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 2, 2, 4, 4, 4, 4, 4, 4, 4], [4, 2, 2, 4, 4, 4, 2, 2, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4]]}]}