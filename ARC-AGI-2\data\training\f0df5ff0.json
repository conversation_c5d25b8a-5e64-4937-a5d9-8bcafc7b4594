{"train": [{"input": [[0, 0, 6, 2, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 4], [0, 0, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, 4, 0, 0], [6, 3, 0, 1, 0, 4, 0, 0, 0, 0, 0, 6, 0, 0, 0], [0, 0, 4, 0, 6, 0, 0, 1, 0, 0, 0, 0, 3, 0, 0], [6, 0, 3, 0, 0, 0, 0, 0, 0, 3, 2, 2, 0, 0, 4], [4, 2, 0, 2, 0, 2, 0, 0, 0, 0, 6, 0, 0, 6, 0], [0, 0, 0, 0, 2, 6, 0, 6, 0, 0, 4, 0, 0, 0, 0], [0, 6, 0, 0, 0, 0, 4, 0, 0, 0, 4, 6, 0, 0, 0], [0, 0, 0, 6, 0, 6, 0, 0, 3, 3, 4, 0, 6, 6, 0], [4, 6, 0, 3, 1, 3, 0, 0, 4, 0, 0, 2, 6, 0, 0], [0, 0, 3, 2, 0, 4, 0, 6, 0, 0, 4, 3, 6, 0, 0], [0, 4, 0, 0, 0, 0, 0, 2, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 1, 0, 0, 0, 3, 0, 3, 0, 0, 2, 2, 0], [6, 0, 0, 0, 0, 0, 2, 0, 0, 0, 1, 0, 0, 4, 3], [0, 0, 0, 0, 0, 3, 4, 0, 0, 2, 0, 0, 0, 0, 0]], "output": [[0, 0, 6, 2, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 4], [0, 0, 1, 1, 1, 2, 0, 0, 0, 2, 6, 0, 4, 0, 0], [6, 3, 1, 1, 1, 4, 1, 1, 1, 0, 0, 6, 0, 0, 0], [0, 0, 4, 1, 6, 0, 1, 1, 1, 0, 0, 0, 3, 0, 0], [6, 0, 3, 0, 0, 0, 1, 1, 1, 3, 2, 2, 0, 0, 4], [4, 2, 0, 2, 0, 2, 0, 0, 0, 0, 6, 0, 0, 6, 0], [0, 0, 0, 0, 2, 6, 0, 6, 0, 0, 4, 0, 0, 0, 0], [0, 6, 0, 0, 0, 0, 4, 0, 0, 0, 4, 6, 0, 0, 0], [0, 0, 0, 6, 1, 6, 0, 0, 3, 3, 4, 0, 6, 6, 0], [4, 6, 0, 3, 1, 3, 0, 0, 4, 0, 0, 2, 6, 0, 0], [0, 0, 3, 2, 1, 4, 0, 6, 0, 0, 4, 3, 6, 0, 0], [0, 4, 1, 1, 1, 0, 0, 2, 0, 0, 0, 4, 0, 0, 0], [0, 0, 1, 1, 1, 0, 0, 3, 0, 3, 1, 1, 2, 2, 0], [6, 0, 1, 1, 1, 0, 2, 0, 0, 1, 1, 1, 0, 4, 3], [0, 0, 0, 0, 0, 3, 4, 0, 0, 2, 1, 1, 0, 0, 0]]}, {"input": [[3, 0, 0, 0, 0, 0, 0, 9, 2, 3, 0, 2, 3, 3, 0], [2, 2, 2, 3, 0, 0, 3, 5, 7, 0, 0, 0, 2, 7, 0], [0, 3, 2, 2, 0, 0, 0, 7, 0, 5, 0, 0, 0, 5, 0], [0, 0, 0, 0, 2, 0, 0, 0, 0, 9, 0, 0, 2, 9, 2], [8, 0, 0, 3, 0, 0, 1, 2, 8, 2, 0, 0, 0, 0, 0], [3, 0, 0, 3, 2, 0, 0, 0, 7, 0, 2, 0, 3, 0, 0], [0, 0, 3, 0, 0, 0, 3, 0, 0, 5, 6, 0, 2, 0, 0], [0, 1, 0, 2, 3, 6, 0, 0, 2, 3, 0, 2, 0, 6, 0], [0, 2, 8, 0, 3, 0, 0, 0, 6, 0, 7, 0, 0, 3, 0], [0, 2, 3, 0, 8, 0, 0, 3, 0, 1, 0, 0, 6, 0, 0], [7, 0, 3, 0, 0, 2, 0, 0, 0, 0, 0, 0, 6, 7, 0], [0, 0, 2, 0, 5, 2, 0, 0, 0, 7, 0, 0, 0, 0, 0], [0, 9, 0, 2, 0, 0, 0, 0, 0, 2, 0, 0, 3, 0, 0], [0, 0, 2, 0, 2, 3, 3, 0, 0, 0, 1, 0, 0, 6, 2], [0, 2, 9, 0, 0, 5, 2, 3, 0, 0, 0, 0, 2, 0, 0]], "output": [[3, 0, 0, 0, 0, 0, 0, 9, 2, 3, 0, 2, 3, 3, 0], [2, 2, 2, 3, 0, 0, 3, 5, 7, 0, 0, 0, 2, 7, 0], [0, 3, 2, 2, 0, 0, 0, 7, 0, 5, 0, 0, 0, 5, 0], [0, 0, 0, 0, 2, 1, 1, 1, 0, 9, 0, 0, 2, 9, 2], [8, 0, 0, 3, 0, 1, 1, 2, 8, 2, 0, 0, 0, 0, 0], [3, 0, 0, 3, 2, 1, 1, 1, 7, 0, 2, 0, 3, 0, 0], [1, 1, 3, 0, 0, 0, 3, 0, 0, 5, 6, 0, 2, 0, 0], [1, 1, 1, 2, 3, 6, 0, 0, 2, 3, 0, 2, 0, 6, 0], [1, 2, 8, 0, 3, 0, 0, 0, 6, 1, 7, 0, 0, 3, 0], [0, 2, 3, 0, 8, 0, 0, 3, 1, 1, 1, 0, 6, 0, 0], [7, 0, 3, 0, 0, 2, 0, 0, 1, 1, 1, 0, 6, 7, 0], [0, 0, 2, 0, 5, 2, 0, 0, 0, 7, 0, 0, 0, 0, 0], [0, 9, 0, 2, 0, 0, 0, 0, 0, 2, 1, 1, 3, 0, 0], [0, 0, 2, 0, 2, 3, 3, 0, 0, 1, 1, 1, 0, 6, 2], [0, 2, 9, 0, 0, 5, 2, 3, 0, 1, 1, 1, 2, 0, 0]]}, {"input": [[3, 9, 0, 0, 0, 0, 0, 0, 0, 8, 3, 9, 3, 0, 8], [0, 0, 0, 4, 0, 4, 0, 0, 3, 0, 2, 7, 7, 0, 2], [0, 3, 3, 0, 9, 0, 9, 0, 0, 0, 0, 2, 0, 0, 0], [0, 0, 0, 0, 9, 0, 4, 0, 3, 0, 3, 3, 0, 1, 0], [0, 1, 0, 0, 8, 8, 0, 3, 0, 2, 9, 3, 0, 0, 0], [0, 9, 0, 8, 0, 0, 0, 0, 3, 0, 0, 7, 0, 0, 3], [0, 0, 7, 2, 2, 4, 7, 0, 9, 0, 0, 0, 0, 0, 8], [0, 4, 0, 0, 7, 0, 0, 0, 8, 0, 3, 3, 2, 7, 0], [0, 3, 3, 0, 2, 0, 1, 0, 2, 3, 3, 0, 0, 0, 4], [0, 0, 0, 3, 0, 8, 0, 0, 0, 7, 0, 3, 0, 1, 0], [0, 8, 0, 0, 3, 0, 9, 9, 0, 0, 7, 3, 9, 0, 0], [4, 4, 3, 0, 3, 0, 7, 8, 0, 4, 0, 7, 3, 0, 9], [7, 0, 1, 3, 3, 0, 7, 0, 1, 7, 0, 0, 4, 0, 9], [3, 0, 0, 0, 7, 8, 8, 0, 0, 8, 0, 9, 0, 0, 0], [0, 0, 7, 0, 0, 9, 8, 0, 0, 4, 8, 3, 0, 0, 0]], "output": [[3, 9, 0, 0, 0, 0, 0, 0, 0, 8, 3, 9, 3, 0, 8], [0, 0, 0, 4, 0, 4, 0, 0, 3, 0, 2, 7, 7, 0, 2], [0, 3, 3, 0, 9, 0, 9, 0, 0, 0, 0, 2, 1, 1, 1], [1, 1, 1, 0, 9, 0, 4, 0, 3, 0, 3, 3, 1, 1, 1], [1, 1, 1, 0, 8, 8, 0, 3, 0, 2, 9, 3, 1, 1, 1], [1, 9, 1, 8, 0, 0, 0, 0, 3, 0, 0, 7, 0, 0, 3], [0, 0, 7, 2, 2, 4, 7, 0, 9, 0, 0, 0, 0, 0, 8], [0, 4, 0, 0, 7, 1, 1, 1, 8, 0, 3, 3, 2, 7, 0], [0, 3, 3, 0, 2, 1, 1, 1, 2, 3, 3, 0, 1, 1, 4], [0, 0, 0, 3, 0, 8, 1, 1, 0, 7, 0, 3, 1, 1, 1], [0, 8, 0, 0, 3, 0, 9, 9, 0, 0, 7, 3, 9, 1, 1], [4, 4, 3, 1, 3, 0, 7, 8, 1, 4, 0, 7, 3, 0, 9], [7, 1, 1, 3, 3, 0, 7, 1, 1, 7, 0, 0, 4, 0, 9], [3, 1, 1, 1, 7, 8, 8, 1, 1, 8, 0, 9, 0, 0, 0], [0, 0, 7, 0, 0, 9, 8, 0, 0, 4, 8, 3, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 7, 0, 0, 6, 0, 7, 0, 0, 0, 0, 0, 3], [2, 0, 4, 0, 3, 7, 0, 0, 7, 0, 7, 0, 0, 0, 8], [0, 0, 0, 7, 8, 0, 6, 2, 7, 0, 1, 0, 2, 7, 2], [0, 1, 0, 0, 2, 0, 0, 2, 6, 0, 0, 0, 0, 7, 8], [6, 0, 0, 6, 0, 1, 0, 0, 0, 2, 0, 0, 8, 6, 4], [0, 0, 4, 6, 6, 0, 0, 4, 8, 0, 0, 8, 0, 8, 7], [8, 7, 6, 0, 0, 0, 0, 7, 7, 4, 4, 8, 0, 0, 7], [3, 0, 0, 1, 0, 0, 3, 0, 0, 0, 0, 7, 0, 8, 0], [0, 0, 8, 6, 8, 6, 7, 6, 1, 6, 6, 0, 4, 0, 7], [0, 8, 7, 0, 7, 8, 0, 7, 0, 8, 0, 0, 8, 0, 4], [4, 4, 0, 0, 0, 3, 0, 0, 2, 0, 0, 3, 8, 4, 8], [0, 0, 8, 0, 1, 0, 8, 3, 7, 6, 7, 8, 0, 8, 7], [0, 0, 0, 0, 8, 0, 0, 6, 0, 3, 0, 0, 3, 0, 0], [0, 6, 0, 0, 0, 0, 6, 3, 1, 0, 3, 0, 0, 1, 3], [4, 6, 0, 0, 0, 0, 8, 0, 0, 0, 2, 2, 0, 0, 6]], "output": [[0, 0, 0, 7, 0, 0, 6, 0, 7, 0, 0, 0, 0, 0, 3], [2, 0, 4, 0, 3, 7, 0, 0, 7, 1, 7, 1, 0, 0, 8], [1, 1, 1, 7, 8, 0, 6, 2, 7, 1, 1, 1, 2, 7, 2], [1, 1, 1, 0, 2, 1, 1, 2, 6, 1, 1, 1, 0, 7, 8], [6, 1, 1, 6, 1, 1, 1, 0, 0, 2, 0, 0, 8, 6, 4], [0, 0, 4, 6, 6, 1, 1, 4, 8, 0, 0, 8, 0, 8, 7], [8, 7, 6, 1, 1, 0, 0, 7, 7, 4, 4, 8, 0, 0, 7], [3, 0, 1, 1, 1, 0, 3, 1, 1, 1, 0, 7, 0, 8, 0], [0, 0, 8, 6, 8, 6, 7, 6, 1, 6, 6, 0, 4, 0, 7], [0, 8, 7, 0, 7, 8, 0, 7, 1, 8, 0, 0, 8, 0, 4], [4, 4, 0, 1, 1, 3, 0, 0, 2, 0, 0, 3, 8, 4, 8], [0, 0, 8, 1, 1, 1, 8, 3, 7, 6, 7, 8, 0, 8, 7], [0, 0, 0, 1, 8, 1, 0, 6, 1, 3, 0, 0, 3, 1, 1], [0, 6, 0, 0, 0, 0, 6, 3, 1, 1, 3, 0, 1, 1, 3], [4, 6, 0, 0, 0, 0, 8, 1, 1, 1, 2, 2, 1, 1, 6]]}]}