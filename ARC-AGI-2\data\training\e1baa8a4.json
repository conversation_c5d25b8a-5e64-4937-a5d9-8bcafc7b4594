{"train": [{"input": [[1, 1, 1, 2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8], [1, 1, 1, 2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8], [1, 1, 1, 2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8], [1, 1, 1, 2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8], [1, 1, 1, 2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8], [5, 5, 5, 6, 6, 6, 6, 6, 3, 3, 3, 3, 3, 3], [5, 5, 5, 6, 6, 6, 6, 6, 3, 3, 3, 3, 3, 3], [5, 5, 5, 6, 6, 6, 6, 6, 3, 3, 3, 3, 3, 3], [5, 5, 5, 6, 6, 6, 6, 6, 3, 3, 3, 3, 3, 3], [5, 5, 5, 6, 6, 6, 6, 6, 3, 3, 3, 3, 3, 3], [5, 5, 5, 6, 6, 6, 6, 6, 3, 3, 3, 3, 3, 3], [5, 5, 5, 6, 6, 6, 6, 6, 3, 3, 3, 3, 3, 3], [5, 5, 5, 6, 6, 6, 6, 6, 3, 3, 3, 3, 3, 3], [5, 5, 5, 6, 6, 6, 6, 6, 3, 3, 3, 3, 3, 3], [5, 5, 5, 6, 6, 6, 6, 6, 3, 3, 3, 3, 3, 3]], "output": [[1, 2, 8], [5, 6, 3]]}, {"input": [[4, 4, 4, 4, 4, 5, 5, 5, 2, 2, 2, 2, 2, 2], [4, 4, 4, 4, 4, 5, 5, 5, 2, 2, 2, 2, 2, 2], [4, 4, 4, 4, 4, 5, 5, 5, 2, 2, 2, 2, 2, 2], [4, 4, 4, 4, 4, 5, 5, 5, 2, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 3, 3, 3, 2, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 3, 3, 3, 2, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 3, 3, 3, 2, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 3, 3, 3, 2, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 3, 3, 3, 2, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 3, 3, 3, 2, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 3, 3, 3, 2, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 3, 3, 3, 2, 2, 2, 2, 2, 2]], "output": [[4, 5, 2], [1, 3, 2]]}, {"input": [[8, 8, 8, 7, 7, 7, 7, 7, 9, 9, 9, 9, 8, 8, 8], [8, 8, 8, 7, 7, 7, 7, 7, 9, 9, 9, 9, 8, 8, 8], [8, 8, 8, 7, 7, 7, 7, 7, 9, 9, 9, 9, 8, 8, 8], [8, 8, 8, 7, 7, 7, 7, 7, 9, 9, 9, 9, 8, 8, 8], [3, 3, 3, 1, 1, 1, 1, 1, 6, 6, 6, 6, 4, 4, 4], [3, 3, 3, 1, 1, 1, 1, 1, 6, 6, 6, 6, 4, 4, 4], [3, 3, 3, 1, 1, 1, 1, 1, 6, 6, 6, 6, 4, 4, 4], [3, 3, 3, 1, 1, 1, 1, 1, 6, 6, 6, 6, 4, 4, 4], [3, 3, 3, 1, 1, 1, 1, 1, 6, 6, 6, 6, 4, 4, 4], [2, 2, 2, 4, 4, 4, 4, 4, 1, 1, 1, 1, 5, 5, 5], [2, 2, 2, 4, 4, 4, 4, 4, 1, 1, 1, 1, 5, 5, 5], [2, 2, 2, 4, 4, 4, 4, 4, 1, 1, 1, 1, 5, 5, 5], [2, 2, 2, 4, 4, 4, 4, 4, 1, 1, 1, 1, 5, 5, 5], [2, 2, 2, 4, 4, 4, 4, 4, 1, 1, 1, 1, 5, 5, 5], [2, 2, 2, 4, 4, 4, 4, 4, 1, 1, 1, 1, 5, 5, 5]], "output": [[8, 7, 9, 8], [3, 1, 6, 4], [2, 4, 1, 5]]}, {"input": [[2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [3, 3, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [3, 3, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [3, 3, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [3, 3, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5]], "output": [[2, 8], [3, 5]]}], "test": [{"input": [[8, 8, 8, 8, 7, 7, 7, 7, 4, 4, 4, 4, 4, 8, 8], [8, 8, 8, 8, 7, 7, 7, 7, 4, 4, 4, 4, 4, 8, 8], [3, 3, 3, 3, 1, 1, 1, 1, 2, 2, 2, 2, 2, 8, 8], [3, 3, 3, 3, 1, 1, 1, 1, 2, 2, 2, 2, 2, 8, 8], [3, 3, 3, 3, 1, 1, 1, 1, 2, 2, 2, 2, 2, 8, 8], [4, 4, 4, 4, 5, 5, 5, 5, 3, 3, 3, 3, 3, 9, 9], [4, 4, 4, 4, 5, 5, 5, 5, 3, 3, 3, 3, 3, 9, 9], [4, 4, 4, 4, 5, 5, 5, 5, 3, 3, 3, 3, 3, 9, 9], [4, 4, 4, 4, 5, 5, 5, 5, 3, 3, 3, 3, 3, 9, 9], [4, 4, 4, 4, 5, 5, 5, 5, 3, 3, 3, 3, 3, 9, 9], [2, 2, 2, 2, 6, 6, 6, 6, 1, 1, 1, 1, 1, 7, 7], [2, 2, 2, 2, 6, 6, 6, 6, 1, 1, 1, 1, 1, 7, 7], [1, 1, 1, 1, 5, 5, 5, 5, 2, 2, 2, 2, 2, 8, 8], [1, 1, 1, 1, 5, 5, 5, 5, 2, 2, 2, 2, 2, 8, 8], [1, 1, 1, 1, 5, 5, 5, 5, 2, 2, 2, 2, 2, 8, 8]], "output": [[8, 7, 4, 8], [3, 1, 2, 8], [4, 5, 3, 9], [2, 6, 1, 7], [1, 5, 2, 8]]}]}