#!/usr/bin/env python3
"""
Advanced Transformation System for ARC-AGI-2

This module implements sophisticated transformations that go beyond basic
geometric and color operations to handle complex ARC puzzle patterns.

Key Features:
- Pattern-based transformations
- Shape manipulation and extraction
- Template matching and filling
- Region-based operations
- Conditional transformations
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Callable
from collections import Counter, defaultdict
from scipy import ndimage
from scipy.ndimage import label, binary_erosion, binary_dilation
import copy

from arc_solver_components import (
    find_connected_components, 
    extract_all_features,
    BasicTransformations,
    ColorTransformations
)

class PatternTransformations:
    """
    Pattern-based transformations for ARC grids.
    """
    
    @staticmethod
    def tile_pattern(grid: np.ndarray, tile_x: int, tile_y: int) -> np.ndarray:
        """
        Tile a pattern across a larger grid.
        
        Args:
            grid: Source pattern to tile
            tile_x: Number of tiles horizontally
            tile_y: Number of tiles vertically
            
        Returns:
            Tiled grid
        """
        return np.tile(grid, (tile_y, tile_x))
    
    @staticmethod
    def extract_repeating_pattern(grid: np.ndarray) -> Tuple[np.ndarray, int, int]:
        """
        Extract the smallest repeating pattern from a grid.
        
        Args:
            grid: Input grid
            
        Returns:
            Tuple of (pattern, repeat_x, repeat_y)
        """
        h, w = grid.shape
        
        # Try different pattern sizes
        for pattern_h in range(1, h + 1):
            for pattern_w in range(1, w + 1):
                if h % pattern_h == 0 and w % pattern_w == 0:
                    pattern = grid[:pattern_h, :pattern_w]
                    
                    # Check if this pattern tiles to recreate the original
                    tiled = PatternTransformations.tile_pattern(
                        pattern, w // pattern_w, h // pattern_h
                    )
                    
                    if np.array_equal(tiled, grid):
                        return pattern, w // pattern_w, h // pattern_h
        
        # If no repeating pattern found, return the whole grid
        return grid, 1, 1
    
    @staticmethod
    def fill_pattern(grid: np.ndarray, pattern: np.ndarray, 
                    target_color: int = 0) -> np.ndarray:
        """
        Fill regions of target_color with the given pattern.
        
        Args:
            grid: Input grid
            pattern: Pattern to fill with
            target_color: Color to replace
            
        Returns:
            Grid with pattern filled
        """
        result = grid.copy()
        pattern_h, pattern_w = pattern.shape
        
        # Find all positions of target color
        target_positions = np.where(grid == target_color)
        
        for row, col in zip(target_positions[0], target_positions[1]):
            # Calculate pattern position
            pattern_row = row % pattern_h
            pattern_col = col % pattern_w
            result[row, col] = pattern[pattern_row, pattern_col]
        
        return result
    
    @staticmethod
    def create_border(grid: np.ndarray, border_color: int, 
                     border_width: int = 1) -> np.ndarray:
        """
        Add a border around the grid.
        
        Args:
            grid: Input grid
            border_color: Color for the border
            border_width: Width of the border
            
        Returns:
            Grid with border added
        """
        h, w = grid.shape
        new_h = h + 2 * border_width
        new_w = w + 2 * border_width
        
        result = np.full((new_h, new_w), border_color, dtype=grid.dtype)
        result[border_width:border_width+h, border_width:border_width+w] = grid
        
        return result
    
    @staticmethod
    def remove_border(grid: np.ndarray, border_width: int = 1) -> np.ndarray:
        """
        Remove border from the grid.
        
        Args:
            grid: Input grid
            border_width: Width of border to remove
            
        Returns:
            Grid with border removed
        """
        h, w = grid.shape
        if h <= 2 * border_width or w <= 2 * border_width:
            return grid  # Can't remove border
        
        return grid[border_width:h-border_width, border_width:w-border_width]

class ShapeTransformations:
    """
    Shape-based transformations for ARC grids.
    """
    
    @staticmethod
    def extract_largest_shape(grid: np.ndarray, background_color: int = 0) -> np.ndarray:
        """
        Extract the largest connected component as a new grid.
        
        Args:
            grid: Input grid
            background_color: Background color to ignore
            
        Returns:
            Grid containing only the largest shape
        """
        components_info = find_connected_components(grid, background_color)
        
        if not components_info:
            return grid.copy()
        
        # Find the largest component
        largest_size = 0
        largest_color = None
        largest_component = None
        
        for color, color_info in components_info.items():
            for component in color_info['components']:
                if component['size'] > largest_size:
                    largest_size = component['size']
                    largest_color = color
                    largest_component = component
        
        if largest_component is None:
            return grid.copy()
        
        # Create result grid with only the largest shape
        result = np.full_like(grid, background_color)
        for row, col in largest_component['coords']:
            result[row, col] = largest_color
        
        return result
    
    @staticmethod
    def duplicate_shape(grid: np.ndarray, shape_color: int, 
                       offset_row: int, offset_col: int,
                       background_color: int = 0) -> np.ndarray:
        """
        Duplicate a shape at a new position.
        
        Args:
            grid: Input grid
            shape_color: Color of shape to duplicate
            offset_row: Row offset for duplication
            offset_col: Column offset for duplication
            background_color: Background color
            
        Returns:
            Grid with duplicated shape
        """
        result = grid.copy()
        h, w = grid.shape
        
        # Find all positions of the shape
        shape_positions = np.where(grid == shape_color)
        
        for row, col in zip(shape_positions[0], shape_positions[1]):
            new_row = row + offset_row
            new_col = col + offset_col
            
            # Check bounds
            if 0 <= new_row < h and 0 <= new_col < w:
                result[new_row, new_col] = shape_color
        
        return result
    
    @staticmethod
    def scale_shape(grid: np.ndarray, scale_factor: int,
                   background_color: int = 0) -> np.ndarray:
        """
        Scale shapes in the grid by a factor.
        
        Args:
            grid: Input grid
            scale_factor: Integer scaling factor
            background_color: Background color
            
        Returns:
            Grid with scaled shapes
        """
        h, w = grid.shape
        new_h = h * scale_factor
        new_w = w * scale_factor
        
        result = np.full((new_h, new_w), background_color, dtype=grid.dtype)
        
        for row in range(h):
            for col in range(w):
                if grid[row, col] != background_color:
                    # Fill the scaled region
                    start_row = row * scale_factor
                    end_row = start_row + scale_factor
                    start_col = col * scale_factor
                    end_col = start_col + scale_factor
                    
                    result[start_row:end_row, start_col:end_col] = grid[row, col]
        
        return result
    
    @staticmethod
    def connect_shapes(grid: np.ndarray, color1: int, color2: int,
                      connection_color: int) -> np.ndarray:
        """
        Connect two shapes with a line of specified color.
        
        Args:
            grid: Input grid
            color1: First shape color
            color2: Second shape color
            connection_color: Color for the connection line
            
        Returns:
            Grid with shapes connected
        """
        result = grid.copy()
        
        # Find centroids of both shapes
        pos1 = np.where(grid == color1)
        pos2 = np.where(grid == color2)
        
        if len(pos1[0]) == 0 or len(pos2[0]) == 0:
            return result
        
        centroid1 = (int(np.mean(pos1[0])), int(np.mean(pos1[1])))
        centroid2 = (int(np.mean(pos2[0])), int(np.mean(pos2[1])))
        
        # Draw line between centroids (simple Bresenham-like algorithm)
        r1, c1 = centroid1
        r2, c2 = centroid2
        
        # Simple line drawing
        steps = max(abs(r2 - r1), abs(c2 - c1))
        if steps == 0:
            return result
        
        for i in range(steps + 1):
            t = i / steps
            r = int(r1 + t * (r2 - r1))
            c = int(c1 + t * (c2 - c1))
            
            if 0 <= r < grid.shape[0] and 0 <= c < grid.shape[1]:
                result[r, c] = connection_color
        
        return result

class ConditionalTransformations:
    """
    Conditional transformations based on grid properties.
    """
    
    @staticmethod
    def transform_if_symmetric(grid: np.ndarray, 
                              transformation: Callable[[np.ndarray], np.ndarray],
                              symmetry_type: str = 'horizontal') -> np.ndarray:
        """
        Apply transformation only if grid has specified symmetry.
        
        Args:
            grid: Input grid
            transformation: Transformation function to apply
            symmetry_type: Type of symmetry to check
            
        Returns:
            Transformed grid if symmetric, original otherwise
        """
        features = extract_all_features(grid)
        
        is_symmetric = False
        if symmetry_type == 'horizontal':
            is_symmetric = features.get('symmetry_horizontal_symmetry', False)
        elif symmetry_type == 'vertical':
            is_symmetric = features.get('symmetry_vertical_symmetry', False)
        elif symmetry_type == 'rotational':
            is_symmetric = features.get('symmetry_rotation_180_symmetry', False)
        
        if is_symmetric:
            return transformation(grid)
        else:
            return grid.copy()
    
    @staticmethod
    def transform_by_size(grid: np.ndarray, 
                         small_transform: Callable[[np.ndarray], np.ndarray],
                         large_transform: Callable[[np.ndarray], np.ndarray],
                         size_threshold: int = 50) -> np.ndarray:
        """
        Apply different transformations based on grid size.
        
        Args:
            grid: Input grid
            small_transform: Transformation for small grids
            large_transform: Transformation for large grids
            size_threshold: Size threshold
            
        Returns:
            Transformed grid
        """
        grid_size = grid.shape[0] * grid.shape[1]
        
        if grid_size <= size_threshold:
            return small_transform(grid)
        else:
            return large_transform(grid)
    
    @staticmethod
    def transform_by_color_count(grid: np.ndarray,
                               few_colors_transform: Callable[[np.ndarray], np.ndarray],
                               many_colors_transform: Callable[[np.ndarray], np.ndarray],
                               color_threshold: int = 3) -> np.ndarray:
        """
        Apply different transformations based on number of colors.
        
        Args:
            grid: Input grid
            few_colors_transform: Transformation for few colors
            many_colors_transform: Transformation for many colors
            color_threshold: Color count threshold
            
        Returns:
            Transformed grid
        """
        num_colors = len(np.unique(grid))
        
        if num_colors <= color_threshold:
            return few_colors_transform(grid)
        else:
            return many_colors_transform(grid)

class CompositeTransformations:
    """
    Complex transformations that combine multiple operations.
    """
    
    @staticmethod
    def mirror_and_extend(grid: np.ndarray, direction: str = 'horizontal') -> np.ndarray:
        """
        Mirror the grid and extend it in the specified direction.
        
        Args:
            grid: Input grid
            direction: 'horizontal' or 'vertical'
            
        Returns:
            Extended grid with mirrored content
        """
        if direction == 'horizontal':
            mirrored = BasicTransformations.flip_horizontal(grid)
            return np.concatenate([grid, mirrored], axis=1)
        else:  # vertical
            mirrored = BasicTransformations.flip_vertical(grid)
            return np.concatenate([grid, mirrored], axis=0)
    
    @staticmethod
    def create_symmetric_pattern(grid: np.ndarray) -> np.ndarray:
        """
        Create a 4-way symmetric pattern from the input.
        
        Args:
            grid: Input grid (should be small, like a quarter pattern)
            
        Returns:
            4-way symmetric pattern
        """
        # Create horizontal mirror
        h_mirror = BasicTransformations.flip_horizontal(grid)
        top_half = np.concatenate([grid, h_mirror], axis=1)
        
        # Create vertical mirror of the top half
        bottom_half = BasicTransformations.flip_vertical(top_half)
        
        # Combine to create full symmetric pattern
        return np.concatenate([top_half, bottom_half], axis=0)
    
    @staticmethod
    def extract_and_replicate(grid: np.ndarray, background_color: int = 0) -> np.ndarray:
        """
        Extract the main pattern and replicate it to fill the grid.
        
        Args:
            grid: Input grid
            background_color: Background color
            
        Returns:
            Grid with replicated pattern
        """
        # Find the bounding box of non-background content
        non_bg_positions = np.where(grid != background_color)
        
        if len(non_bg_positions[0]) == 0:
            return grid.copy()
        
        min_row, max_row = non_bg_positions[0].min(), non_bg_positions[0].max()
        min_col, max_col = non_bg_positions[1].min(), non_bg_positions[1].max()
        
        # Extract the pattern
        pattern = grid[min_row:max_row+1, min_col:max_col+1]
        
        # Calculate how many times it can fit
        pattern_h, pattern_w = pattern.shape
        grid_h, grid_w = grid.shape
        
        tiles_y = grid_h // pattern_h
        tiles_x = grid_w // pattern_w
        
        if tiles_x > 0 and tiles_y > 0:
            return PatternTransformations.tile_pattern(pattern, tiles_x, tiles_y)
        else:
            return grid.copy()

def get_all_advanced_transformations() -> Dict[str, Callable]:
    """
    Get dictionary of all advanced transformations.
    
    Returns:
        Dictionary mapping transformation names to functions
    """
    transformations = {}
    
    # Pattern transformations
    transformations.update({
        'extract_largest_shape': ShapeTransformations.extract_largest_shape,
        'create_border': lambda grid: PatternTransformations.create_border(grid, 1),
        'remove_border': PatternTransformations.remove_border,
        'mirror_and_extend_h': lambda grid: CompositeTransformations.mirror_and_extend(grid, 'horizontal'),
        'mirror_and_extend_v': lambda grid: CompositeTransformations.mirror_and_extend(grid, 'vertical'),
        'create_symmetric_pattern': CompositeTransformations.create_symmetric_pattern,
        'extract_and_replicate': CompositeTransformations.extract_and_replicate,
        'scale_2x': lambda grid: ShapeTransformations.scale_shape(grid, 2),
        'scale_3x': lambda grid: ShapeTransformations.scale_shape(grid, 3),
    })
    
    return transformations
