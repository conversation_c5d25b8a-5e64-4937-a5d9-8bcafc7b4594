#!/usr/bin/env python3
"""
Test script for the large-scale evaluation system.

This script tests the evaluation infrastructure on a small subset of puzzles
to ensure everything works before running the full evaluation.
"""

import sys
import time
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from arc_large_scale_evaluation import ARCDatasetLoader, LargeScaleEvaluator
from arc_solver_components import *

def test_dataset_loading():
    """Test dataset loading functionality."""
    print("Testing dataset loading...")
    
    loader = ARCDatasetLoader()
    
    # Test loading a small subset of training data
    training_data = loader.load_training_dataset(max_puzzles=5, shuffle=False)
    
    if len(training_data) > 0:
        print(f"✓ Successfully loaded {len(training_data)} training puzzles")
        
        # Test statistics
        stats = loader.get_dataset_statistics(training_data)
        print(f"  - Total train examples: {stats['total_train_examples']}")
        print(f"  - Total test examples: {stats['total_test_examples']}")
        print(f"  - Average grid area: {stats.get('avg_grid_area', 0):.1f}")
        print(f"  - Average colors per grid: {stats.get('avg_colors_per_grid', 0):.1f}")
        
        return training_data
    else:
        print("✗ Failed to load training data")
        return None

def test_feature_extraction():
    """Test feature extraction on sample grids."""
    print("\nTesting feature extraction...")
    
    # Create a simple test grid
    test_grid = np.array([
        [0, 1, 0],
        [1, 2, 1],
        [0, 1, 0]
    ])
    
    try:
        features = extract_all_features(test_grid)
        print(f"✓ Extracted {len(features)} features from test grid")
        print(f"  - Grid shape: {features['grid_shape']}")
        print(f"  - Colors: {features['color_num_colors']}")
        print(f"  - Symmetries: {features['symmetry_num_symmetries']}")
        return True
    except Exception as e:
        print(f"✗ Feature extraction failed: {e}")
        return False

def test_solver_creation():
    """Test solver creation and basic functionality."""
    print("\nTesting solver creation...")
    
    try:
        # Create solver without feature database first
        solver = HybridARCSolver()
        print("✓ Basic solver created successfully")
        
        # Test with a simple puzzle
        test_input = np.array([[1, 2], [3, 4]])
        training_examples = [
            {
                'input': np.array([[0, 1], [2, 3]]),
                'output': np.array([[1, 0], [3, 2]])  # Simple flip
            }
        ]
        
        solution = solver.solve_puzzle(
            test_input=test_input,
            training_examples=training_examples,
            use_retrieval=False,  # No feature DB
            use_heuristics=True
        )
        
        print(f"✓ Solver produced solution with method: {solution['method']}")
        print(f"  - Confidence: {solution['confidence']:.3f}")
        print(f"  - Processing time: {solution['processing_time']:.3f}s")
        
        return solver
    except Exception as e:
        print(f"✗ Solver creation failed: {e}")
        return None

def test_evaluation_system(training_data, solver):
    """Test the evaluation system on a small dataset."""
    print("\nTesting evaluation system...")
    
    if not training_data or not solver:
        print("✗ Cannot test evaluation - missing training data or solver")
        return None
    
    try:
        # Create evaluator
        evaluator = LargeScaleEvaluator(solver, max_runtime_per_puzzle=10.0)
        
        # Test on first 2 puzzles
        test_data = dict(list(training_data.items())[:2])
        
        print(f"Running evaluation on {len(test_data)} puzzles...")
        stats = evaluator.evaluate_dataset(
            test_data,
            max_puzzles=2,
            save_results=False
        )
        
        print(f"✓ Evaluation completed successfully")
        print(f"  - Solve rate: {stats.solve_rate:.1%}")
        print(f"  - Average partial score: {stats.avg_partial_score:.3f}")
        print(f"  - Average runtime: {stats.avg_runtime:.2f}s")
        
        return stats
    except Exception as e:
        print(f"✗ Evaluation failed: {e}")
        return None

def test_feature_database_creation(training_data):
    """Test feature database creation."""
    print("\nTesting feature database creation...")
    
    if not training_data:
        print("✗ Cannot test feature database - no training data")
        return None
    
    try:
        # Build feature database from 2 puzzles
        test_data = dict(list(training_data.items())[:2])
        feature_db = build_feature_database(test_data, max_puzzles=2)
        
        print(f"✓ Feature database created successfully")
        print(f"  - Puzzles: {feature_db['metadata']['total_puzzles']}")
        print(f"  - Examples: {feature_db['metadata']['total_examples']}")
        print(f"  - Features: {len(feature_db['metadata']['feature_names'])}")
        
        return feature_db
    except Exception as e:
        print(f"✗ Feature database creation failed: {e}")
        return None

def test_solver_with_feature_db(training_data, feature_db):
    """Test solver with feature database."""
    print("\nTesting solver with feature database...")
    
    if not training_data or not feature_db:
        print("✗ Cannot test - missing training data or feature database")
        return None
    
    try:
        # Create solver with feature database
        solver = HybridARCSolver(
            feature_db=feature_db,
            max_retrieval_candidates=3,
            max_transformation_depth=2
        )
        
        # Test on a puzzle
        puzzle_id = list(training_data.keys())[0]
        puzzle_data = training_data[puzzle_id]
        
        if puzzle_data.get('test'):
            test_input = np.array(puzzle_data['test'][0]['input'])
            training_examples = [
                {
                    'input': np.array(ex['input']),
                    'output': np.array(ex['output'])
                }
                for ex in puzzle_data.get('train', [])
            ]
            
            solution = solver.solve_puzzle(
                test_input=test_input,
                training_examples=training_examples,
                use_retrieval=True,
                use_heuristics=True
            )
            
            print(f"✓ Solver with feature DB produced solution")
            print(f"  - Method: {solution['method']}")
            print(f"  - Confidence: {solution['confidence']:.3f}")
            print(f"  - Retrieval results: {len(solution['retrieval_results'])}")
            
            return True
        else:
            print("✗ No test examples in puzzle")
            return False
            
    except Exception as e:
        print(f"✗ Solver with feature DB failed: {e}")
        return False

def main():
    """Run all tests."""
    print("ARC-AGI-2 Large-Scale Evaluation System Test")
    print("=" * 50)
    
    # Test 1: Dataset loading
    training_data = test_dataset_loading()
    
    # Test 2: Feature extraction
    feature_extraction_ok = test_feature_extraction()
    
    # Test 3: Basic solver
    solver = test_solver_creation()
    
    # Test 4: Evaluation system
    eval_stats = test_evaluation_system(training_data, solver)
    
    # Test 5: Feature database
    feature_db = test_feature_database_creation(training_data)
    
    # Test 6: Solver with feature database
    solver_with_db_ok = test_solver_with_feature_db(training_data, feature_db)
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    tests = [
        ("Dataset Loading", training_data is not None),
        ("Feature Extraction", feature_extraction_ok),
        ("Basic Solver", solver is not None),
        ("Evaluation System", eval_stats is not None),
        ("Feature Database", feature_db is not None),
        ("Solver with Feature DB", solver_with_db_ok)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:25} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The evaluation system is ready for use.")
        print("\nNext steps:")
        print("1. Run: python run_large_scale_evaluation.py --max_puzzles 50")
        print("2. Analyze results and optimize solver parameters")
        print("3. Scale up to full 400+ puzzle evaluation")
    else:
        print(f"\n⚠ {total - passed} tests failed. Please fix issues before proceeding.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
