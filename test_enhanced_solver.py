#!/usr/bin/env python3
"""
Test Enhanced Solver Performance

This script compares the performance of the enhanced solver with advanced
transformations against the original solver.
"""

import sys
import time
import numpy as np
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from arc_large_scale_evaluation import ARCDatasetLoader, LargeScaleEvaluator
from arc_solver_components import HybridARCSolver, build_feature_database
from enhanced_solver import EnhancedARCSolver

def test_solver_comparison():
    """Compare original and enhanced solvers on a small dataset."""
    print("Enhanced Solver Comparison Test")
    print("=" * 50)
    
    # Load a small dataset
    loader = ARCDatasetLoader()
    training_data = loader.load_training_dataset(max_puzzles=15, shuffle=False)
    
    if len(training_data) < 10:
        print("✗ Not enough training data for comparison")
        return False
    
    # Split data: 5 for feature DB, 10 for testing
    all_puzzle_ids = list(training_data.keys())
    feature_puzzle_ids = all_puzzle_ids[:5]
    test_puzzle_ids = all_puzzle_ids[5:15]
    
    feature_data = {pid: training_data[pid] for pid in feature_puzzle_ids}
    test_data = {pid: training_data[pid] for pid in test_puzzle_ids}
    
    print(f"Using {len(feature_data)} puzzles for feature database")
    print(f"Testing on {len(test_data)} puzzles")
    
    # Build feature database
    print("\nBuilding feature database...")
    feature_db = build_feature_database(feature_data, max_puzzles=5)
    
    # Create both solvers
    print("\nCreating solvers...")
    original_solver = HybridARCSolver(
        feature_db=feature_db,
        max_retrieval_candidates=3,
        max_transformation_depth=2
    )
    
    enhanced_solver = EnhancedARCSolver(
        feature_db=feature_db,
        max_retrieval_candidates=3,
        max_transformation_depth=2
    )
    
    print("✓ Both solvers created successfully")
    
    # Test both solvers
    print("\n" + "=" * 50)
    print("TESTING ORIGINAL SOLVER")
    print("=" * 50)
    
    original_evaluator = LargeScaleEvaluator(original_solver, max_runtime_per_puzzle=15.0)
    original_stats = original_evaluator.evaluate_dataset(
        test_data, max_puzzles=10, save_results=False
    )
    
    print("\n" + "=" * 50)
    print("TESTING ENHANCED SOLVER")
    print("=" * 50)
    
    enhanced_evaluator = LargeScaleEvaluator(enhanced_solver, max_runtime_per_puzzle=15.0)
    
    # Modify the evaluator to use the enhanced solve method
    class EnhancedEvaluator(LargeScaleEvaluator):
        def evaluate_puzzle(self, puzzle_id: str, puzzle_data: dict):
            """Override to use enhanced solving method."""
            start_time = time.time()
            
            try:
                train_examples = puzzle_data.get('train', [])
                test_examples = puzzle_data.get('test', [])
                
                if not test_examples:
                    return self._create_failed_result(puzzle_id, "No test examples", 0.0)
                
                # Convert training examples
                training_examples = []
                for example in train_examples:
                    training_examples.append({
                        'input': np.array(example['input']),
                        'output': np.array(example['output'])
                    })
                
                test_input = np.array(test_examples[0]['input'])
                
                # Check timeout
                if time.time() - start_time > self.max_runtime_per_puzzle:
                    return self._create_failed_result(puzzle_id, "Timeout", time.time() - start_time)
                
                # Use enhanced solving method
                solution = self.solver.solve_puzzle_enhanced(
                    test_input=test_input,
                    training_examples=training_examples,
                    use_retrieval=True,
                    use_heuristics=True,
                    use_smart_search=True
                )
                
                runtime = time.time() - start_time
                
                # Calculate scores
                solved = False
                partial_score = 0.0
                
                expected_output = None
                if 'output' in test_examples[0]:
                    expected_output = np.array(test_examples[0]['output'])
                
                if solution['predicted_output'] is not None and expected_output is not None:
                    predicted = solution['predicted_output']
                    if np.array_equal(predicted, expected_output):
                        solved = True
                        partial_score = 1.0
                    else:
                        if predicted.shape == expected_output.shape:
                            partial_score = np.mean(predicted == expected_output)
                        else:
                            partial_score = 0.1
                
                from arc_large_scale_evaluation import PuzzleResult
                return PuzzleResult(
                    puzzle_id=puzzle_id,
                    solved=solved,
                    partial_score=partial_score,
                    runtime=runtime,
                    method=solution.get('method', 'unknown'),
                    confidence=solution.get('confidence', 0.0),
                    transformation_sequence=solution.get('transformation_sequence', [])
                )
                
            except Exception as e:
                runtime = time.time() - start_time
                return self._create_failed_result(puzzle_id, str(e), runtime)
        
        def _create_failed_result(self, puzzle_id, error_msg, runtime):
            from arc_large_scale_evaluation import PuzzleResult
            return PuzzleResult(
                puzzle_id=puzzle_id,
                solved=False,
                partial_score=0.0,
                runtime=runtime,
                method="error",
                confidence=0.0,
                transformation_sequence=[],
                error_message=error_msg
            )
    
    enhanced_evaluator = EnhancedEvaluator(enhanced_solver, max_runtime_per_puzzle=15.0)
    enhanced_stats = enhanced_evaluator.evaluate_dataset(
        test_data, max_puzzles=10, save_results=False
    )
    
    # Compare results
    print("\n" + "=" * 60)
    print("COMPARISON RESULTS")
    print("=" * 60)
    
    print(f"{'Metric':<25} {'Original':<15} {'Enhanced':<15} {'Improvement':<15}")
    print("-" * 70)
    
    # Solve rate
    orig_solve_rate = original_stats.solve_rate
    enh_solve_rate = enhanced_stats.solve_rate
    solve_improvement = enh_solve_rate - orig_solve_rate
    print(f"{'Solve Rate':<25} {orig_solve_rate:<15.1%} {enh_solve_rate:<15.1%} {solve_improvement:<15.1%}")
    
    # Partial score
    orig_partial = original_stats.avg_partial_score
    enh_partial = enhanced_stats.avg_partial_score
    partial_improvement = enh_partial - orig_partial
    print(f"{'Avg Partial Score':<25} {orig_partial:<15.3f} {enh_partial:<15.3f} {partial_improvement:<15.3f}")
    
    # Runtime
    orig_runtime = original_stats.avg_runtime
    enh_runtime = enhanced_stats.avg_runtime
    runtime_change = enh_runtime - orig_runtime
    print(f"{'Avg Runtime (s)':<25} {orig_runtime:<15.2f} {enh_runtime:<15.2f} {runtime_change:<15.2f}")
    
    # Method breakdown
    print(f"\nOriginal Method Breakdown:")
    for method, count in original_stats.method_breakdown.items():
        print(f"  {method}: {count} ({100*count/original_stats.total_puzzles:.1f}%)")
    
    print(f"\nEnhanced Method Breakdown:")
    for method, count in enhanced_stats.method_breakdown.items():
        print(f"  {method}: {count} ({100*count/enhanced_stats.total_puzzles:.1f}%)")
    
    # Overall assessment
    print(f"\n{'='*60}")
    print("ASSESSMENT")
    print("="*60)
    
    improvements = 0
    if solve_improvement > 0:
        print("✓ Enhanced solver has better solve rate")
        improvements += 1
    elif solve_improvement == 0:
        print("~ Enhanced solver has same solve rate")
    else:
        print("✗ Enhanced solver has worse solve rate")
    
    if partial_improvement > 0.05:
        print("✓ Enhanced solver has significantly better partial scores")
        improvements += 1
    elif partial_improvement > 0:
        print("~ Enhanced solver has slightly better partial scores")
    else:
        print("✗ Enhanced solver has worse partial scores")
    
    if runtime_change < 2.0:
        print("✓ Enhanced solver runtime is acceptable")
        improvements += 1
    else:
        print("✗ Enhanced solver is significantly slower")
    
    if improvements >= 2:
        print(f"\n🎉 Enhanced solver shows clear improvements!")
        print("Recommended to use enhanced solver for better performance.")
    elif improvements == 1:
        print(f"\n~ Enhanced solver shows some improvements.")
        print("Consider using enhanced solver depending on priorities.")
    else:
        print(f"\n⚠ Enhanced solver needs more work.")
        print("Stick with original solver for now.")
    
    return improvements >= 2

def test_specific_transformations():
    """Test specific advanced transformations."""
    print("\n" + "=" * 50)
    print("TESTING ADVANCED TRANSFORMATIONS")
    print("=" * 50)
    
    from advanced_transformations import (
        PatternTransformations, ShapeTransformations, 
        CompositeTransformations, get_all_advanced_transformations
    )
    
    # Test pattern tiling
    print("Testing pattern tiling...")
    pattern = np.array([[1, 2], [3, 4]])
    tiled = PatternTransformations.tile_pattern(pattern, 2, 2)
    expected = np.array([[1, 2, 1, 2], [3, 4, 3, 4], [1, 2, 1, 2], [3, 4, 3, 4]])
    
    if np.array_equal(tiled, expected):
        print("✓ Pattern tiling works correctly")
    else:
        print("✗ Pattern tiling failed")
    
    # Test shape scaling
    print("Testing shape scaling...")
    small_shape = np.array([[0, 1], [1, 0]])
    scaled = ShapeTransformations.scale_shape(small_shape, 2)
    
    if scaled.shape == (4, 4):
        print("✓ Shape scaling works correctly")
    else:
        print("✗ Shape scaling failed")
    
    # Test mirror and extend
    print("Testing mirror and extend...")
    test_grid = np.array([[1, 2], [3, 4]])
    mirrored = CompositeTransformations.mirror_and_extend(test_grid, 'horizontal')
    
    if mirrored.shape == (2, 4):
        print("✓ Mirror and extend works correctly")
    else:
        print("✗ Mirror and extend failed")
    
    # Test all transformations are accessible
    all_transforms = get_all_advanced_transformations()
    print(f"✓ {len(all_transforms)} advanced transformations available")
    
    return True

def main():
    """Run all tests."""
    print("Enhanced ARC Solver Testing")
    print("=" * 50)
    
    # Test advanced transformations
    transforms_ok = test_specific_transformations()
    
    # Test solver comparison
    comparison_ok = test_solver_comparison()
    
    print("\n" + "=" * 50)
    print("FINAL SUMMARY")
    print("=" * 50)
    
    if transforms_ok and comparison_ok:
        print("🎉 All tests passed! Enhanced solver is ready for deployment.")
        print("\nNext steps:")
        print("1. Run large-scale evaluation with enhanced solver")
        print("2. Fine-tune transformation priorities")
        print("3. Add more domain-specific transformations")
    elif transforms_ok:
        print("~ Transformations work, but solver needs improvement.")
        print("Consider adjusting search strategies or transformation priorities.")
    else:
        print("⚠ Issues found. Please fix before proceeding.")
    
    return transforms_ok and comparison_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
