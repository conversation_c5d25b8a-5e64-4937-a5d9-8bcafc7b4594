{"train": [{"input": [[5, 5, 5, 5, 5, 0, 0, 5, 5, 5], [0, 0, 5, 0, 5, 5, 5, 5, 5, 0], [5, 5, 5, 5, 5, 0, 5, 0, 0, 5], [5, 0, 5, 5, 5, 0, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 0, 5], [5, 5, 5, 5, 0, 5, 5, 5, 5, 5], [0, 0, 5, 5, 0, 5, 0, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 0, 5, 5], [0, 5, 5, 5, 5, 5, 0, 5, 5, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 5]], "output": [[5, 5, 5, 5, 5, 2, 2, 5, 5, 5], [2, 2, 5, 3, 5, 5, 5, 5, 5, 3], [5, 5, 5, 5, 5, 2, 5, 2, 2, 5], [5, 3, 5, 5, 5, 2, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 3, 5], [5, 5, 5, 5, 2, 5, 5, 5, 5, 5], [2, 2, 5, 5, 2, 5, 1, 1, 5, 3], [5, 5, 5, 5, 5, 5, 5, 1, 5, 5], [1, 5, 5, 5, 5, 5, 3, 5, 5, 3], [1, 1, 5, 5, 5, 5, 5, 5, 3, 5]]}, {"input": [[5, 5, 5, 5, 0, 5, 5, 5, 0, 5], [0, 0, 5, 5, 5, 5, 5, 5, 5, 5], [0, 5, 5, 5, 5, 5, 0, 0, 5, 0], [5, 5, 0, 5, 5, 5, 5, 0, 5, 0], [5, 5, 5, 5, 0, 0, 5, 5, 5, 5], [0, 5, 0, 5, 5, 5, 5, 0, 5, 0], [0, 5, 5, 5, 0, 0, 5, 5, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 0], [0, 5, 5, 5, 5, 5, 5, 0, 5, 0]], "output": [[5, 5, 5, 5, 3, 5, 5, 5, 3, 5], [1, 1, 5, 5, 5, 5, 5, 5, 5, 5], [1, 5, 5, 5, 5, 5, 1, 1, 5, 2], [5, 5, 3, 5, 5, 5, 5, 1, 5, 2], [5, 5, 5, 5, 2, 2, 5, 5, 5, 5], [2, 5, 3, 5, 5, 5, 5, 3, 5, 2], [2, 5, 5, 5, 2, 2, 5, 5, 5, 2], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 2], [3, 5, 5, 5, 5, 5, 5, 3, 5, 2]]}, {"input": [[0, 0, 5, 5, 0, 5, 5, 5, 0, 5], [5, 5, 0, 0, 5, 5, 5, 5, 0, 5], [5, 0, 5, 0, 5, 0, 5, 5, 0, 5], [5, 0, 5, 5, 0, 5, 5, 5, 5, 5], [5, 5, 5, 0, 0, 5, 5, 0, 5, 0], [5, 5, 0, 5, 5, 5, 5, 0, 5, 0], [5, 5, 0, 5, 5, 0, 5, 5, 5, 5], [5, 5, 5, 0, 5, 5, 5, 5, 5, 5], [5, 0, 5, 5, 5, 0, 5, 0, 5, 5], [5, 5, 0, 5, 5, 5, 5, 5, 5, 5]], "output": [[2, 2, 5, 5, 3, 5, 5, 5, 1, 5], [5, 5, 1, 1, 5, 5, 5, 5, 1, 5], [5, 2, 5, 1, 5, 3, 5, 5, 1, 5], [5, 2, 5, 5, 1, 5, 5, 5, 5, 5], [5, 5, 5, 1, 1, 5, 5, 2, 5, 2], [5, 5, 2, 5, 5, 5, 5, 2, 5, 2], [5, 5, 2, 5, 5, 3, 5, 5, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 5, 5], [5, 3, 5, 5, 5, 3, 5, 3, 5, 5], [5, 5, 3, 5, 5, 5, 5, 5, 5, 5]]}], "test": [{"input": [[0, 5, 5, 5, 5, 5, 0, 0, 5, 5], [5, 5, 5, 0, 5, 5, 0, 5, 0, 5], [5, 5, 0, 5, 5, 5, 5, 5, 0, 5], [5, 0, 0, 5, 5, 5, 5, 5, 5, 5], [0, 5, 5, 5, 5, 5, 0, 5, 5, 5], [0, 5, 5, 0, 5, 5, 0, 5, 0, 0], [5, 5, 0, 5, 5, 5, 5, 5, 0, 5], [5, 5, 0, 5, 5, 5, 5, 5, 5, 0], [0, 0, 5, 5, 5, 5, 0, 5, 5, 5], [5, 5, 5, 5, 0, 5, 0, 0, 5, 0]], "output": [[3, 5, 5, 5, 5, 5, 1, 1, 5, 5], [5, 5, 5, 3, 5, 5, 1, 5, 2, 5], [5, 5, 1, 5, 5, 5, 5, 5, 2, 5], [5, 1, 1, 5, 5, 5, 5, 5, 5, 5], [2, 5, 5, 5, 5, 5, 2, 5, 5, 5], [2, 5, 5, 3, 5, 5, 2, 5, 1, 1], [5, 5, 2, 5, 5, 5, 5, 5, 1, 5], [5, 5, 2, 5, 5, 5, 5, 5, 5, 3], [2, 2, 5, 5, 5, 5, 1, 5, 5, 5], [5, 5, 5, 5, 3, 5, 1, 1, 5, 3]]}]}