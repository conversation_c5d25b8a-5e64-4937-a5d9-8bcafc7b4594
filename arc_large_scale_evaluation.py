#!/usr/bin/env python3
"""
ARC-AGI-2 Large-Scale Evaluation System

This module provides infrastructure for running the ARC solver on large datasets,
tracking performance metrics, and optimizing for leaderboard performance.

Key Features:
- Load and process 400+ training puzzles
- Track solve rates, partial solutions, and runtime
- Feature-based similarity analysis
- Performance profiling and optimization
- Kaggle submission generation
"""

import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
import time
import random
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass
import pickle
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class PuzzleResult:
    """Data class for storing puzzle solving results."""
    puzzle_id: str
    solved: bool
    partial_score: float
    runtime: float
    method: str
    confidence: float
    transformation_sequence: List[str]
    error_message: Optional[str] = None

@dataclass
class EvaluationStats:
    """Data class for storing evaluation statistics."""
    total_puzzles: int
    solved_puzzles: int
    solve_rate: float
    avg_runtime: float
    total_runtime: float
    avg_partial_score: float
    method_breakdown: Dict[str, int]
    runtime_distribution: List[float]

class ARCDatasetLoader:
    """
    Efficient loader for the full ARC-AGI-2 dataset with caching and filtering.
    """
    
    def __init__(self, data_dir: str = "ARC-AGI-2/data"):
        self.data_dir = Path(data_dir)
        self.training_dir = self.data_dir / "training"
        self.evaluation_dir = self.data_dir / "evaluation"
        self._training_cache = {}
        self._evaluation_cache = {}
    
    def load_training_dataset(self, max_puzzles: Optional[int] = None, 
                            shuffle: bool = True, seed: int = 42) -> Dict[str, Any]:
        """
        Load training dataset with optional limits and shuffling.
        
        Args:
            max_puzzles: Maximum number of puzzles to load
            shuffle: Whether to shuffle the puzzle order
            seed: Random seed for reproducibility
            
        Returns:
            Dictionary containing training puzzles
        """
        logger.info(f"Loading training dataset from {self.training_dir}...")
        
        # Get all training files
        training_files = list(self.training_dir.glob("*.json"))
        logger.info(f"Found {len(training_files)} training files")
        
        if shuffle:
            random.seed(seed)
            random.shuffle(training_files)
        
        if max_puzzles:
            training_files = training_files[:max_puzzles]
            logger.info(f"Limited to {len(training_files)} puzzles")
        
        training_data = {}
        failed_loads = 0
        
        for i, file_path in enumerate(training_files):
            if i % 50 == 0:
                logger.info(f"  Loading puzzle {i+1}/{len(training_files)}...")
            
            puzzle_id = file_path.stem
            try:
                with open(file_path, 'r') as f:
                    puzzle_data = json.load(f)
                training_data[puzzle_id] = puzzle_data
            except Exception as e:
                logger.warning(f"  Failed to load {puzzle_id}: {e}")
                failed_loads += 1
                continue
        
        logger.info(f"✓ Loaded {len(training_data)} training puzzles ({failed_loads} failed)")
        return training_data
    
    def load_evaluation_dataset(self) -> Dict[str, Any]:
        """
        Load evaluation dataset.
        
        Returns:
            Dictionary containing evaluation puzzles
        """
        logger.info(f"Loading evaluation dataset from {self.evaluation_dir}...")
        
        evaluation_files = list(self.evaluation_dir.glob("*.json"))
        evaluation_data = {}
        failed_loads = 0
        
        for file_path in evaluation_files:
            puzzle_id = file_path.stem
            try:
                with open(file_path, 'r') as f:
                    puzzle_data = json.load(f)
                evaluation_data[puzzle_id] = puzzle_data
            except Exception as e:
                logger.warning(f"  Failed to load {puzzle_id}: {e}")
                failed_loads += 1
                continue
        
        logger.info(f"✓ Loaded {len(evaluation_data)} evaluation puzzles ({failed_loads} failed)")
        return evaluation_data
    
    def get_dataset_statistics(self, dataset: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get comprehensive statistics about a dataset.
        
        Args:
            dataset: Dataset dictionary
            
        Returns:
            Dictionary containing statistics
        """
        stats = {
            'total_puzzles': len(dataset),
            'total_train_examples': 0,
            'total_test_examples': 0,
            'grid_sizes': [],
            'color_counts': [],
            'complexity_scores': []
        }
        
        for puzzle_id, puzzle_data in dataset.items():
            train_examples = puzzle_data.get('train', [])
            test_examples = puzzle_data.get('test', [])
            
            stats['total_train_examples'] += len(train_examples)
            stats['total_test_examples'] += len(test_examples)
            
            # Analyze grid sizes and colors
            puzzle_complexity = 0
            for example in train_examples + test_examples:
                if 'input' in example:
                    input_grid = np.array(example['input'])
                    stats['grid_sizes'].append(input_grid.shape)
                    unique_colors = len(np.unique(input_grid))
                    stats['color_counts'].append(unique_colors)
                    puzzle_complexity += input_grid.size * unique_colors
                
                if 'output' in example:
                    output_grid = np.array(example['output'])
                    stats['grid_sizes'].append(output_grid.shape)
                    unique_colors = len(np.unique(output_grid))
                    stats['color_counts'].append(unique_colors)
                    puzzle_complexity += output_grid.size * unique_colors
            
            stats['complexity_scores'].append(puzzle_complexity)
        
        # Calculate summary statistics
        if stats['grid_sizes']:
            areas = [h * w for h, w in stats['grid_sizes']]
            stats['avg_grid_area'] = np.mean(areas)
            stats['max_grid_area'] = max(areas)
            stats['min_grid_area'] = min(areas)
            stats['grid_area_std'] = np.std(areas)
        
        if stats['color_counts']:
            stats['avg_colors_per_grid'] = np.mean(stats['color_counts'])
            stats['max_colors_per_grid'] = max(stats['color_counts'])
            stats['min_colors_per_grid'] = min(stats['color_counts'])
        
        if stats['complexity_scores']:
            stats['avg_complexity'] = np.mean(stats['complexity_scores'])
            stats['max_complexity'] = max(stats['complexity_scores'])
            stats['min_complexity'] = min(stats['complexity_scores'])
        
        return stats

class LargeScaleEvaluator:
    """
    Large-scale evaluation system for ARC solver performance.
    """
    
    def __init__(self, solver, max_runtime_per_puzzle: float = 30.0):
        """
        Initialize the evaluator.
        
        Args:
            solver: ARC solver instance
            max_runtime_per_puzzle: Maximum runtime per puzzle in seconds
        """
        self.solver = solver
        self.max_runtime_per_puzzle = max_runtime_per_puzzle
        self.results = []
        
    def evaluate_puzzle(self, puzzle_id: str, puzzle_data: Dict[str, Any]) -> PuzzleResult:
        """
        Evaluate solver on a single puzzle.
        
        Args:
            puzzle_id: Puzzle identifier
            puzzle_data: Puzzle data dictionary
            
        Returns:
            PuzzleResult containing evaluation metrics
        """
        start_time = time.time()
        
        try:
            # Get training examples
            train_examples = puzzle_data.get('train', [])
            test_examples = puzzle_data.get('test', [])
            
            if not test_examples:
                return PuzzleResult(
                    puzzle_id=puzzle_id,
                    solved=False,
                    partial_score=0.0,
                    runtime=0.0,
                    method="no_test_data",
                    confidence=0.0,
                    transformation_sequence=[],
                    error_message="No test examples found"
                )
            
            # Convert training examples to expected format
            training_examples = []
            for example in train_examples:
                training_examples.append({
                    'input': np.array(example['input']),
                    'output': np.array(example['output'])
                })
            
            # Solve the first test example
            test_input = np.array(test_examples[0]['input'])
            
            # Check for timeout
            if time.time() - start_time > self.max_runtime_per_puzzle:
                return PuzzleResult(
                    puzzle_id=puzzle_id,
                    solved=False,
                    partial_score=0.0,
                    runtime=time.time() - start_time,
                    method="timeout",
                    confidence=0.0,
                    transformation_sequence=[],
                    error_message="Timeout exceeded"
                )
            
            # Solve using the solver
            solution = self.solver.solve_puzzle(
                test_input=test_input,
                training_examples=training_examples,
                use_retrieval=True,
                use_heuristics=True
            )
            
            runtime = time.time() - start_time
            
            # Check if we have the expected output for scoring
            expected_output = None
            if 'output' in test_examples[0]:
                expected_output = np.array(test_examples[0]['output'])
            
            # Calculate scores
            solved = False
            partial_score = 0.0
            
            if solution['predicted_output'] is not None and expected_output is not None:
                predicted = solution['predicted_output']
                if np.array_equal(predicted, expected_output):
                    solved = True
                    partial_score = 1.0
                else:
                    # Calculate partial score based on similarity
                    if predicted.shape == expected_output.shape:
                        partial_score = np.mean(predicted == expected_output)
                    else:
                        partial_score = 0.1  # Small credit for producing output
            
            return PuzzleResult(
                puzzle_id=puzzle_id,
                solved=solved,
                partial_score=partial_score,
                runtime=runtime,
                method=solution.get('method', 'unknown'),
                confidence=solution.get('confidence', 0.0),
                transformation_sequence=solution.get('transformation_sequence', [])
            )
            
        except Exception as e:
            runtime = time.time() - start_time
            logger.error(f"Error evaluating puzzle {puzzle_id}: {e}")
            
            return PuzzleResult(
                puzzle_id=puzzle_id,
                solved=False,
                partial_score=0.0,
                runtime=runtime,
                method="error",
                confidence=0.0,
                transformation_sequence=[],
                error_message=str(e)
            )

    def evaluate_dataset(self, dataset: Dict[str, Any],
                        max_puzzles: Optional[int] = None,
                        save_results: bool = True,
                        results_file: str = "evaluation_results.pkl") -> EvaluationStats:
        """
        Evaluate solver on entire dataset.

        Args:
            dataset: Dataset dictionary
            max_puzzles: Maximum number of puzzles to evaluate
            save_results: Whether to save detailed results
            results_file: File to save results to

        Returns:
            EvaluationStats containing summary statistics
        """
        logger.info(f"Starting large-scale evaluation on {len(dataset)} puzzles...")

        puzzle_ids = list(dataset.keys())
        if max_puzzles:
            puzzle_ids = puzzle_ids[:max_puzzles]

        self.results = []
        start_time = time.time()

        for i, puzzle_id in enumerate(puzzle_ids):
            if i % 10 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / max(i, 1)
                remaining = (len(puzzle_ids) - i) * avg_time
                logger.info(f"Progress: {i+1}/{len(puzzle_ids)} "
                          f"({100*(i+1)/len(puzzle_ids):.1f}%) "
                          f"ETA: {remaining/60:.1f}min")

            puzzle_data = dataset[puzzle_id]
            result = self.evaluate_puzzle(puzzle_id, puzzle_data)
            self.results.append(result)

            # Log significant results
            if result.solved:
                logger.info(f"✓ Solved {puzzle_id} in {result.runtime:.2f}s "
                          f"using {result.method}")
            elif result.partial_score > 0.5:
                logger.info(f"~ Partial solution for {puzzle_id}: "
                          f"{result.partial_score:.2f} score")

        total_runtime = time.time() - start_time

        # Calculate statistics
        solved_count = sum(1 for r in self.results if r.solved)
        solve_rate = solved_count / len(self.results)
        avg_runtime = np.mean([r.runtime for r in self.results])
        avg_partial_score = np.mean([r.partial_score for r in self.results])

        method_breakdown = Counter(r.method for r in self.results)
        runtime_distribution = [r.runtime for r in self.results]

        stats = EvaluationStats(
            total_puzzles=len(self.results),
            solved_puzzles=solved_count,
            solve_rate=solve_rate,
            avg_runtime=avg_runtime,
            total_runtime=total_runtime,
            avg_partial_score=avg_partial_score,
            method_breakdown=dict(method_breakdown),
            runtime_distribution=runtime_distribution
        )

        # Save results if requested
        if save_results:
            self.save_results(results_file)

        # Log summary
        logger.info(f"\n{'='*60}")
        logger.info(f"EVALUATION COMPLETE")
        logger.info(f"{'='*60}")
        logger.info(f"Total puzzles: {stats.total_puzzles}")
        logger.info(f"Solved puzzles: {stats.solved_puzzles}")
        logger.info(f"Solve rate: {stats.solve_rate:.1%}")
        logger.info(f"Average partial score: {stats.avg_partial_score:.3f}")
        logger.info(f"Average runtime per puzzle: {stats.avg_runtime:.2f}s")
        logger.info(f"Total evaluation time: {stats.total_runtime/60:.1f}min")
        logger.info(f"Method breakdown: {stats.method_breakdown}")

        return stats

    def save_results(self, filename: str):
        """Save detailed results to file."""
        with open(filename, 'wb') as f:
            pickle.dump(self.results, f)
        logger.info(f"Results saved to {filename}")

    def load_results(self, filename: str):
        """Load results from file."""
        with open(filename, 'rb') as f:
            self.results = pickle.load(f)
        logger.info(f"Results loaded from {filename}")

    def generate_performance_report(self, output_dir: str = "performance_analysis"):
        """
        Generate comprehensive performance analysis report.

        Args:
            output_dir: Directory to save analysis files
        """
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        if not self.results:
            logger.error("No results available for analysis")
            return

        # Create DataFrame for analysis
        df_data = []
        for result in self.results:
            df_data.append({
                'puzzle_id': result.puzzle_id,
                'solved': result.solved,
                'partial_score': result.partial_score,
                'runtime': result.runtime,
                'method': result.method,
                'confidence': result.confidence,
                'num_transformations': len(result.transformation_sequence),
                'has_error': result.error_message is not None
            })

        df = pd.DataFrame(df_data)

        # Save detailed results CSV
        df.to_csv(output_path / "detailed_results.csv", index=False)

        # Generate visualizations
        self._create_performance_plots(df, output_path)

        # Generate summary report
        self._create_summary_report(df, output_path)

        logger.info(f"Performance report generated in {output_path}")

    def _create_performance_plots(self, df: pd.DataFrame, output_path: Path):
        """Create performance visualization plots."""
        plt.style.use('default')

        # 1. Solve rate by method
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # Method breakdown
        method_counts = df['method'].value_counts()
        axes[0, 0].pie(method_counts.values, labels=method_counts.index, autopct='%1.1f%%')
        axes[0, 0].set_title('Solution Methods Distribution')

        # Runtime distribution
        axes[0, 1].hist(df['runtime'], bins=30, alpha=0.7, edgecolor='black')
        axes[0, 1].set_xlabel('Runtime (seconds)')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].set_title('Runtime Distribution')
        axes[0, 1].axvline(df['runtime'].mean(), color='red', linestyle='--',
                          label=f'Mean: {df["runtime"].mean():.2f}s')
        axes[0, 1].legend()

        # Partial score distribution
        axes[1, 0].hist(df['partial_score'], bins=20, alpha=0.7, edgecolor='black')
        axes[1, 0].set_xlabel('Partial Score')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].set_title('Partial Score Distribution')

        # Runtime vs Partial Score
        scatter = axes[1, 1].scatter(df['runtime'], df['partial_score'],
                                   c=df['solved'], cmap='RdYlGn', alpha=0.6)
        axes[1, 1].set_xlabel('Runtime (seconds)')
        axes[1, 1].set_ylabel('Partial Score')
        axes[1, 1].set_title('Runtime vs Partial Score')
        plt.colorbar(scatter, ax=axes[1, 1], label='Solved')

        plt.tight_layout()
        plt.savefig(output_path / "performance_overview.png", dpi=300, bbox_inches='tight')
        plt.close()

        # 2. Method performance comparison
        fig, ax = plt.subplots(figsize=(12, 6))
        method_stats = df.groupby('method').agg({
            'solved': 'mean',
            'partial_score': 'mean',
            'runtime': 'mean'
        }).round(3)

        x = range(len(method_stats))
        width = 0.25

        ax.bar([i - width for i in x], method_stats['solved'], width,
               label='Solve Rate', alpha=0.8)
        ax.bar(x, method_stats['partial_score'], width,
               label='Avg Partial Score', alpha=0.8)
        ax.bar([i + width for i in x], method_stats['runtime']/10, width,
               label='Avg Runtime (÷10)', alpha=0.8)

        ax.set_xlabel('Method')
        ax.set_ylabel('Score / Rate')
        ax.set_title('Performance by Method')
        ax.set_xticks(x)
        ax.set_xticklabels(method_stats.index, rotation=45)
        ax.legend()
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(output_path / "method_comparison.png", dpi=300, bbox_inches='tight')
        plt.close()

    def _create_summary_report(self, df: pd.DataFrame, output_path: Path):
        """Create text summary report."""
        with open(output_path / "summary_report.txt", 'w') as f:
            f.write("ARC-AGI-2 SOLVER PERFORMANCE REPORT\n")
            f.write("=" * 50 + "\n\n")

            # Overall statistics
            f.write("OVERALL PERFORMANCE:\n")
            f.write(f"Total puzzles evaluated: {len(df)}\n")
            f.write(f"Puzzles solved: {df['solved'].sum()}\n")
            f.write(f"Solve rate: {df['solved'].mean():.1%}\n")
            f.write(f"Average partial score: {df['partial_score'].mean():.3f}\n")
            f.write(f"Average runtime: {df['runtime'].mean():.2f}s\n")
            f.write(f"Total runtime: {df['runtime'].sum()/60:.1f} minutes\n\n")

            # Method breakdown
            f.write("METHOD BREAKDOWN:\n")
            method_stats = df.groupby('method').agg({
                'solved': ['count', 'sum', 'mean'],
                'partial_score': 'mean',
                'runtime': 'mean'
            }).round(3)

            for method in method_stats.index:
                count = method_stats.loc[method, ('solved', 'count')]
                solved = method_stats.loc[method, ('solved', 'sum')]
                solve_rate = method_stats.loc[method, ('solved', 'mean')]
                avg_score = method_stats.loc[method, ('partial_score', 'mean')]
                avg_runtime = method_stats.loc[method, ('runtime', 'mean')]

                f.write(f"  {method}:\n")
                f.write(f"    Count: {count}\n")
                f.write(f"    Solved: {solved} ({solve_rate:.1%})\n")
                f.write(f"    Avg partial score: {avg_score:.3f}\n")
                f.write(f"    Avg runtime: {avg_runtime:.2f}s\n\n")

            # Top performing puzzles
            f.write("TOP 10 FASTEST SOLVED PUZZLES:\n")
            solved_df = df[df['solved'] == True].nsmallest(10, 'runtime')
            for _, row in solved_df.iterrows():
                f.write(f"  {row['puzzle_id']}: {row['runtime']:.2f}s ({row['method']})\n")

            f.write("\nTOP 10 HIGHEST PARTIAL SCORES (unsolved):\n")
            unsolved_df = df[df['solved'] == False].nlargest(10, 'partial_score')
            for _, row in unsolved_df.iterrows():
                f.write(f"  {row['puzzle_id']}: {row['partial_score']:.3f} ({row['method']})\n")

        logger.info("Summary report created")
