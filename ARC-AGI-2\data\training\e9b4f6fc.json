{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0], [0, 0, 8, 8, 2, 4, 8, 8, 8, 0, 0, 0, 0], [0, 0, 8, 2, 2, 4, 4, 4, 8, 0, 0, 0, 0], [0, 0, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 2, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[8, 8, 8, 8, 8, 8, 8], [8, 8, 3, 1, 8, 8, 8], [8, 3, 3, 1, 1, 1, 8], [8, 8, 8, 8, 8, 8, 8]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 6, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 3, 3, 0, 4, 5, 0, 0], [0, 3, 5, 5, 5, 5, 3, 3, 0, 0, 0, 0, 0], [0, 3, 3, 1, 1, 5, 3, 3, 0, 0, 0, 0, 0], [0, 3, 8, 1, 1, 3, 3, 3, 0, 0, 0, 0, 0], [0, 3, 8, 8, 8, 8, 3, 3, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0]], "output": [[3, 3, 3, 3, 3, 3, 3], [3, 4, 4, 4, 4, 3, 3], [3, 3, 2, 2, 4, 3, 3], [3, 6, 2, 2, 3, 3, 3], [3, 6, 6, 6, 6, 3, 3], [3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 2, 4, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 4, 1, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[1, 2, 2, 1], [3, 3, 2, 1], [3, 3, 1, 1], [1, 1, 1, 1]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 0, 0, 0], [0, 0, 0, 0, 8, 8, 4, 8, 8, 8, 0, 0, 0], [0, 0, 0, 0, 8, 4, 4, 4, 8, 8, 0, 0, 0], [0, 0, 0, 0, 8, 3, 4, 3, 8, 8, 0, 0, 0], [0, 0, 0, 0, 8, 3, 3, 3, 8, 8, 0, 0, 0], [0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[8, 8, 8, 8, 8, 8], [8, 8, 1, 8, 8, 8], [8, 1, 1, 1, 8, 8], [8, 2, 1, 2, 8, 8], [8, 2, 2, 2, 8, 8], [8, 8, 8, 8, 8, 8]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 1, 3, 1, 1, 0, 0, 0, 0, 0, 0], [0, 0, 1, 3, 3, 3, 1, 0, 0, 0, 0, 0, 0], [0, 0, 1, 8, 8, 8, 1, 0, 0, 0, 0, 0, 0], [0, 0, 1, 7, 7, 7, 1, 0, 0, 0, 0, 0, 0], [0, 0, 1, 1, 7, 1, 1, 0, 0, 4, 8, 0, 0], [0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 6, 7, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[1, 1, 2, 1, 1], [1, 2, 2, 2, 1], [1, 4, 4, 4, 1], [1, 6, 6, 6, 1], [1, 1, 6, 1, 1], [1, 1, 1, 1, 1]]}]}