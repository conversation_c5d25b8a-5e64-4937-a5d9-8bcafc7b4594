#!/usr/bin/env python3
"""
Enhanced ARC Solver with Advanced Transformations

This module provides an improved solver that incorporates advanced transformations
and more sophisticated search strategies.
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Callable
import time
from collections import Counter, defaultdict
import itertools

from arc_solver_components import (
    HybridARCSolver, TransformationSearcher, SolutionEvaluator,
    ARCHeuristics, BasicTransformations, ColorTransformations,
    extract_all_features
)
from advanced_transformations import get_all_advanced_transformations
from advanced_retrieval import AdvancedRetrievalSystem

class EnhancedTransformationSearcher(TransformationSearcher):
    """
    Enhanced transformation searcher with advanced operations.
    """
    
    def __init__(self, max_depth: int = 3, max_candidates: int = 100):
        super().__init__(max_depth, max_candidates)
        
        # Add advanced transformations
        self.available_transforms.update(get_all_advanced_transformations())
        
        # Transformation categories for smarter search
        self.geometric_transforms = [
            'identity', 'rotate_90', 'rotate_180', 'rotate_270',
            'flip_horizontal', 'flip_vertical', 'transpose'
        ]
        
        self.color_transforms = [
            'replace_color', 'invert_colors', 'swap_colors', 'normalize_colors'
        ]
        
        self.pattern_transforms = [
            'extract_largest_shape', 'create_border', 'remove_border',
            'mirror_and_extend_h', 'mirror_and_extend_v', 'create_symmetric_pattern',
            'extract_and_replicate', 'scale_2x', 'scale_3x'
        ]
    
    def smart_search_transformations(self, input_grid: np.ndarray, 
                                   target_grid: np.ndarray,
                                   input_features: Dict[str, Any] = None,
                                   target_features: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Intelligent transformation search based on feature analysis.
        
        Args:
            input_grid: Input grid
            target_grid: Target output grid
            input_features: Pre-computed input features
            target_features: Pre-computed target features
            
        Returns:
            List of candidate solutions with scores
        """
        if input_features is None:
            input_features = extract_all_features(input_grid)
        if target_features is None:
            target_features = extract_all_features(target_grid)
        
        candidates = []
        
        # 1. Try single transformations based on feature differences
        priority_transforms = self._get_priority_transforms(input_features, target_features)
        
        for transform_name in priority_transforms:
            try:
                result_grid = self._apply_single_transform(input_grid, transform_name)
                scores = self.evaluator.comprehensive_score(result_grid, target_grid)
                
                candidate = {
                    'sequence': [transform_name],
                    'result_grid': result_grid,
                    'scores': scores,
                    'overall_score': scores['overall'],
                    'method': 'smart_single'
                }
                
                candidates.append(candidate)
                
                # Early termination if perfect match
                if scores['exact_match'] == 1.0:
                    return [candidate]
                    
            except Exception as e:
                continue
        
        # 2. Try combinations of high-scoring single transformations
        if candidates:
            # Sort by score and take top candidates
            candidates.sort(key=lambda x: x['overall_score'], reverse=True)
            top_transforms = [c['sequence'][0] for c in candidates[:5] if c['overall_score'] > 0.3]
            
            # Try pairs of promising transformations
            for t1, t2 in itertools.combinations(top_transforms, 2):
                try:
                    sequence = [t1, t2]
                    result_grid = self._apply_sequence(input_grid, sequence)
                    scores = self.evaluator.comprehensive_score(result_grid, target_grid)
                    
                    candidate = {
                        'sequence': sequence,
                        'result_grid': result_grid,
                        'scores': scores,
                        'overall_score': scores['overall'],
                        'method': 'smart_pair'
                    }
                    
                    candidates.append(candidate)
                    
                    if scores['exact_match'] == 1.0:
                        return [candidate]
                        
                except Exception as e:
                    continue
        
        # 3. Try pattern-specific transformations
        pattern_candidates = self._try_pattern_transformations(
            input_grid, target_grid, input_features, target_features
        )
        candidates.extend(pattern_candidates)
        
        # Sort all candidates by score
        candidates.sort(key=lambda x: x['overall_score'], reverse=True)
        return candidates[:self.max_candidates]
    
    def _get_priority_transforms(self, input_features: Dict[str, Any], 
                               target_features: Dict[str, Any]) -> List[str]:
        """Get priority transformations based on feature analysis."""
        priority_transforms = []
        
        # Shape-based priorities
        input_shape = input_features['grid_shape']
        target_shape = target_features['grid_shape']
        
        if input_shape != target_shape:
            # Size change suggests scaling, border, or pattern operations
            if target_shape[0] > input_shape[0] or target_shape[1] > input_shape[1]:
                priority_transforms.extend(['create_border', 'scale_2x', 'scale_3x', 
                                          'mirror_and_extend_h', 'mirror_and_extend_v'])
            elif target_shape[0] < input_shape[0] or target_shape[1] < input_shape[1]:
                priority_transforms.extend(['remove_border', 'extract_largest_shape'])
        
        # Geometric transformations based on dimensions
        if input_shape[0] == target_shape[1] and input_shape[1] == target_shape[0]:
            priority_transforms.extend(['rotate_90', 'rotate_270', 'transpose'])
        
        # Symmetry-based priorities
        input_symmetries = input_features.get('symmetry_num_symmetries', 0)
        target_symmetries = target_features.get('symmetry_num_symmetries', 0)
        
        if target_symmetries > input_symmetries:
            priority_transforms.extend(['create_symmetric_pattern', 'mirror_and_extend_h', 
                                      'mirror_and_extend_v'])
        
        # Color-based priorities
        input_colors = set(input_features.get('color_unique_colors', []))
        target_colors = set(target_features.get('color_unique_colors', []))
        
        if input_colors != target_colors:
            priority_transforms.extend(self.color_transforms)
        
        # Add geometric transforms as fallback
        priority_transforms.extend(self.geometric_transforms)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_transforms = []
        for transform in priority_transforms:
            if transform not in seen:
                seen.add(transform)
                unique_transforms.append(transform)
        
        return unique_transforms
    
    def _try_pattern_transformations(self, input_grid: np.ndarray, 
                                   target_grid: np.ndarray,
                                   input_features: Dict[str, Any],
                                   target_features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Try pattern-specific transformations."""
        candidates = []
        
        # Check for tiling patterns
        if self._looks_like_tiling(input_grid, target_grid):
            for transform in ['extract_and_replicate', 'create_symmetric_pattern']:
                try:
                    result_grid = self._apply_single_transform(input_grid, transform)
                    scores = self.evaluator.comprehensive_score(result_grid, target_grid)
                    
                    candidate = {
                        'sequence': [transform],
                        'result_grid': result_grid,
                        'scores': scores,
                        'overall_score': scores['overall'],
                        'method': 'pattern_specific'
                    }
                    
                    candidates.append(candidate)
                    
                except Exception as e:
                    continue
        
        return candidates
    
    def _looks_like_tiling(self, input_grid: np.ndarray, target_grid: np.ndarray) -> bool:
        """Check if the transformation looks like a tiling operation."""
        input_size = input_grid.shape[0] * input_grid.shape[1]
        target_size = target_grid.shape[0] * target_grid.shape[1]
        
        # If target is significantly larger, might be tiling
        if target_size > input_size * 1.5:
            return True
        
        # Check if target dimensions are multiples of input dimensions
        if (target_grid.shape[0] % input_grid.shape[0] == 0 and 
            target_grid.shape[1] % input_grid.shape[1] == 0):
            return True
        
        return False
    
    def _apply_single_transform(self, grid: np.ndarray, transform_name: str) -> np.ndarray:
        """Apply a single transformation with parameter handling."""
        if transform_name not in self.available_transforms:
            raise ValueError(f"Unknown transformation: {transform_name}")
        
        transform_func = self.available_transforms[transform_name]
        
        # Handle parameterized transformations
        if transform_name in ['map_colors', 'replace_color', 'swap_colors']:
            # Skip parameterized transformations for now
            # In a full implementation, you'd infer parameters from context
            return grid.copy()
        else:
            return transform_func(grid)

class EnhancedARCSolver(HybridARCSolver):
    """
    Enhanced ARC solver with advanced transformations and smarter search.
    """
    
    def __init__(self, feature_db: Dict[str, Any] = None, 
                 max_retrieval_candidates: int = 5,
                 max_transformation_depth: int = 3):
        super().__init__(feature_db, max_retrieval_candidates, max_transformation_depth)

        # Replace the searcher with enhanced version
        self.searcher = EnhancedTransformationSearcher(
            max_depth=max_transformation_depth,
            max_candidates=100
        )

        # Replace retrieval system with advanced version
        if feature_db:
            self.retrieval_system = AdvancedRetrievalSystem(feature_db)
    
    def solve_puzzle_enhanced(self, test_input: np.ndarray, 
                            training_examples: List[Dict[str, np.ndarray]] = None,
                            use_retrieval: bool = True,
                            use_heuristics: bool = True,
                            use_smart_search: bool = True) -> Dict[str, Any]:
        """
        Enhanced puzzle solving with smart transformation search.
        
        Args:
            test_input: Test input grid
            training_examples: List of training examples for this puzzle
            use_retrieval: Whether to use retrieval-based reasoning
            use_heuristics: Whether to use rule-based heuristics
            use_smart_search: Whether to use smart transformation search
            
        Returns:
            Dictionary containing solution and metadata
        """
        solution = {
            'predicted_output': None,
            'confidence': 0.0,
            'method': 'unknown',
            'transformation_sequence': [],
            'candidates': [],
            'retrieval_results': [],
            'processing_time': 0.0
        }
        
        start_time = time.time()
        
        try:
            # Extract features from test input
            test_features = extract_all_features(test_input)
            
            candidates = []
            
            # 1. Enhanced training example-based approach
            if training_examples and use_smart_search:
                enhanced_candidates = self._solve_from_training_examples_enhanced(
                    test_input, training_examples, test_features
                )
                candidates.extend(enhanced_candidates)
            elif training_examples:
                # Fallback to original method
                train_candidates = self._solve_from_training_examples(
                    test_input, training_examples, test_features
                )
                candidates.extend(train_candidates)
            
            # 2. Enhanced retrieval-based approach
            if use_retrieval and self.retrieval_system and use_smart_search:
                enhanced_retrieval_candidates = self._solve_from_retrieval_enhanced(
                    test_input, test_features
                )
                candidates.extend(enhanced_retrieval_candidates)
                solution['retrieval_results'] = enhanced_retrieval_candidates[:3]
            elif use_retrieval and self.retrieval_system:
                # Fallback to original retrieval
                retrieval_candidates = self._solve_from_retrieval(
                    test_input, test_features
                )
                candidates.extend(retrieval_candidates)
                solution['retrieval_results'] = retrieval_candidates[:3]
            
            # 3. Enhanced heuristic approach
            if use_heuristics and use_smart_search:
                enhanced_heuristic_candidates = self._solve_from_heuristics_enhanced(
                    test_input, test_features
                )
                candidates.extend(enhanced_heuristic_candidates)
            elif use_heuristics:
                # Fallback to original method
                heuristic_candidates = self._solve_from_heuristics(
                    test_input, test_features
                )
                candidates.extend(heuristic_candidates)
            
            # Select best candidate
            if candidates:
                candidates.sort(key=lambda x: x.get('confidence', 0.0), reverse=True)
                best_candidate = candidates[0]
                
                solution.update({
                    'predicted_output': best_candidate.get('predicted_output'),
                    'confidence': best_candidate.get('confidence', 0.0),
                    'method': best_candidate.get('method', 'unknown'),
                    'transformation_sequence': best_candidate.get('transformation_sequence', []),
                    'candidates': candidates[:5]
                })
            
        except Exception as e:
            solution['method'] = 'error'
            solution['error'] = str(e)
        
        solution['processing_time'] = time.time() - start_time
        return solution
    
    def _solve_from_training_examples_enhanced(self, test_input: np.ndarray, 
                                             training_examples: List[Dict[str, np.ndarray]],
                                             test_features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Enhanced training example-based solving."""
        candidates = []
        
        for i, example in enumerate(training_examples):
            try:
                input_grid = example['input']
                output_grid = example['output']
                
                # Extract features for smart search
                input_features = extract_all_features(input_grid)
                output_features = extract_all_features(output_grid)
                
                # Use smart search
                search_results = self.searcher.smart_search_transformations(
                    input_grid, output_grid, input_features, output_features
                )
                
                # Apply the best transformations to test input
                for result in search_results[:3]:  # Try top 3
                    sequence = result['sequence']
                    
                    try:
                        predicted_output = self.searcher._apply_sequence(test_input, sequence)
                        
                        candidate = {
                            'predicted_output': predicted_output,
                            'confidence': result['overall_score'],
                            'method': f'enhanced_training_{i}',
                            'transformation_sequence': sequence,
                            'source_example': i
                        }
                        
                        candidates.append(candidate)
                        
                    except Exception as e:
                        continue
                        
            except Exception as e:
                continue
        
        return candidates
    
    def _solve_from_heuristics_enhanced(self, test_input: np.ndarray, 
                                      test_features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Enhanced heuristic-based solving."""
        candidates = []
        
        try:
            # Create a dummy target for feature-based search
            # In practice, we don't know the target, so we try common patterns
            common_targets = self._generate_common_target_patterns(test_input, test_features)
            
            for target_pattern in common_targets:
                try:
                    search_results = self.searcher.smart_search_transformations(
                        test_input, target_pattern, test_features
                    )
                    
                    for result in search_results[:2]:  # Try top 2 for each pattern
                        sequence = result['sequence']
                        
                        try:
                            predicted_output = self.searcher._apply_sequence(test_input, sequence)
                            
                            candidate = {
                                'predicted_output': predicted_output,
                                'confidence': 0.2,  # Lower confidence for heuristic
                                'method': 'enhanced_heuristic',
                                'transformation_sequence': sequence
                            }
                            
                            candidates.append(candidate)
                            
                        except Exception as e:
                            continue
                            
                except Exception as e:
                    continue
        
        except Exception as e:
            pass
        
        return candidates
    
    def _generate_common_target_patterns(self, input_grid: np.ndarray, 
                                       input_features: Dict[str, Any]) -> List[np.ndarray]:
        """Generate common target patterns for heuristic search."""
        patterns = []
        
        try:
            # Pattern 1: Doubled size
            patterns.append(np.tile(input_grid, (2, 2)))
            
            # Pattern 2: Mirrored horizontally
            mirrored_h = np.concatenate([input_grid, np.fliplr(input_grid)], axis=1)
            patterns.append(mirrored_h)
            
            # Pattern 3: Mirrored vertically
            mirrored_v = np.concatenate([input_grid, np.flipud(input_grid)], axis=0)
            patterns.append(mirrored_v)
            
            # Pattern 4: With border
            bordered = np.pad(input_grid, 1, mode='constant', constant_values=1)
            patterns.append(bordered)
            
        except Exception as e:
            pass
        
        return patterns[:4]  # Return up to 4 patterns

    def _solve_from_retrieval_enhanced(self, test_input: np.ndarray,
                                     test_features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Enhanced retrieval-based solving with advanced similarity metrics."""
        candidates = []

        if not isinstance(self.retrieval_system, AdvancedRetrievalSystem):
            # Fallback to original retrieval
            return self._solve_from_retrieval(test_input, test_features)

        try:
            # Find similar examples using advanced retrieval
            similar_examples = self.retrieval_system.find_similar_examples(
                test_features, k=self.max_retrieval_candidates * 2
            )

            # Get transformation patterns from similar examples
            transformation_patterns = self.retrieval_system.get_transformation_patterns(
                similar_examples
            )

            # Apply transformation patterns to test input
            for i, pattern in enumerate(transformation_patterns[:self.max_retrieval_candidates]):
                try:
                    # Extract likely transformations from pattern
                    likely_transforms = self._extract_transformations_from_pattern(pattern)

                    for transform_sequence in likely_transforms:
                        try:
                            predicted_output = self.searcher._apply_sequence(test_input, transform_sequence)

                            # Calculate confidence based on pattern confidence and transformation success
                            base_confidence = pattern['confidence']

                            # Boost confidence if transformation makes sense
                            if not np.array_equal(predicted_output, test_input):
                                confidence = base_confidence * 0.8  # Slightly reduce for uncertainty
                            else:
                                confidence = base_confidence * 0.3  # Lower for identity transform

                            candidate = {
                                'predicted_output': predicted_output,
                                'confidence': confidence,
                                'method': f'enhanced_retrieval_{pattern["source_puzzle"]}',
                                'transformation_sequence': transform_sequence,
                                'source_puzzle': pattern['source_puzzle'],
                                'similarity_score': pattern['confidence'],
                                'pattern_info': pattern
                            }

                            candidates.append(candidate)

                        except Exception as e:
                            continue

                except Exception as e:
                    continue

            # Also try direct example-based retrieval
            for example in similar_examples[:3]:
                try:
                    input_grid = example['input_grid']
                    output_grid = example['output_grid']
                    similarity = example['similarity']

                    # Use smart search to find transformations
                    input_features = example['input_features']
                    output_features = example['output_features']

                    search_results = self.searcher.smart_search_transformations(
                        input_grid, output_grid, input_features, output_features
                    )

                    # Apply best transformations to test input
                    for result in search_results[:2]:  # Top 2 per example
                        sequence = result['sequence']

                        try:
                            predicted_output = self.searcher._apply_sequence(test_input, sequence)

                            candidate = {
                                'predicted_output': predicted_output,
                                'confidence': similarity * result['overall_score'] * 0.9,
                                'method': f'enhanced_retrieval_direct_{example["puzzle_id"]}',
                                'transformation_sequence': sequence,
                                'source_puzzle': example['puzzle_id'],
                                'similarity_score': similarity
                            }

                            candidates.append(candidate)

                        except Exception as e:
                            continue

                except Exception as e:
                    continue

        except Exception as e:
            # Fallback to original retrieval on error
            return self._solve_from_retrieval(test_input, test_features)

        return candidates

    def _extract_transformations_from_pattern(self, pattern: Dict[str, Any]) -> List[List[str]]:
        """Extract likely transformation sequences from a transformation pattern."""
        sequences = []

        # Check for geometric transformations
        geometric_transforms = pattern.get('geometric_transformations', {})
        for transform_name, is_present in geometric_transforms.items():
            if is_present:
                sequences.append([transform_name])

        # Check for size changes
        if pattern.get('shape_change', False):
            size_ratio = pattern.get('size_ratio', 1.0)

            if size_ratio > 1.5:
                # Output is larger - might be scaling, tiling, or border
                sequences.extend([
                    ['scale_2x'],
                    ['scale_3x'],
                    ['create_border'],
                    ['mirror_and_extend_h'],
                    ['mirror_and_extend_v'],
                    ['extract_and_replicate']
                ])
            elif size_ratio < 0.7:
                # Output is smaller - might be extraction or border removal
                sequences.extend([
                    ['extract_largest_shape'],
                    ['remove_border']
                ])

        # Check for color changes
        if pattern.get('color_change', False):
            sequences.extend([
                ['replace_color'],
                ['invert_colors'],
                ['normalize_colors']
            ])

        # If no specific patterns found, try common sequences
        if not sequences:
            sequences = [
                ['identity'],
                ['rotate_90'],
                ['rotate_180'],
                ['flip_horizontal'],
                ['flip_vertical']
            ]

        return sequences[:5]  # Return up to 5 sequences
